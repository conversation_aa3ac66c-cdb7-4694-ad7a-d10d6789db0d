# Mobile Apps Management System

<p align="center">
  <img src="https://img.shields.io/badge/Laravel-12-red.svg" alt="Laravel 12">
  <img src="https://img.shields.io/badge/PHP-8.3+-blue.svg" alt="PHP 8.3+">
  <img src="https://img.shields.io/badge/Bootstrap-5.3-purple.svg" alt="Bootstrap 5.3">
  <img src="https://img.shields.io/badge/License-MIT-green.svg" alt="MIT License">
</p>

A comprehensive enterprise-grade mobile application distribution and management platform built with Laravel 12, featuring advanced security, role-based access control, and intelligent metadata extraction.

## 🚀 Features

### Core Functionality
- **Multi-Platform Support**: iOS (IPA), Android (APK), Huawei (APK)
- **Intelligent Metadata Extraction**: Automatic app information parsing from files
- **Sprint-Based Organization**: Project-based app management
- **Secure File Storage**: Enterprise-grade file handling with integrity verification
- **QR Code Generation**: Easy mobile device access
- **Download Analytics**: Comprehensive usage tracking and statistics

### Security Features
- **Quarantine System**: New uploads isolated for security review
- **File Integrity Verification**: SHA-256 hash validation
- **Token-Based Downloads**: Secure access control
- **Audit Logging**: Complete activity tracking
- **Role-Based Permissions**: Granular access control
- **Security Headers**: CSRF, XSS, and content type protection

### User Management
- **Role-Based Access Control**: Admin, Manager, Developer, Viewer roles
- **Permission System**: Granular permissions for all operations
- **User Activity Tracking**: Complete audit trail
- **Session Management**: Secure authentication and authorization

## 📋 System Requirements

### Server Requirements
- **PHP**: 8.1 or higher
- **Database**: MySQL 8.0+ or SQLite 3.8+
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **Storage**: Minimum 10GB for app files
- **Memory**: 512MB RAM minimum, 2GB recommended

### PHP Extensions
- `zip` - For app file processing
- `fileinfo` - For MIME type detection
- `gd` or `imagick` - For image processing
- `openssl` - For encryption
- `pdo_mysql` or `pdo_sqlite` - Database connectivity

### Recommended PHP Configuration
```ini
upload_max_filesize = 500M
post_max_size = 500M
max_execution_time = 300
max_input_time = 300
memory_limit = 512M
```

## 🛠️ Installation

### 1. Clone Repository
```bash
git clone <repository-url>
cd mobile-apps-management
```

### 2. Install Dependencies
```bash
composer install
npm install && npm run build
```

### 3. Environment Configuration
```bash
cp .env.example .env
php artisan key:generate
```

### 4. Database Setup
```bash
# For MySQL
php artisan migrate

# For SQLite (development)
touch database/database.sqlite
php artisan migrate
```

### 5. Storage Setup
```bash
php artisan storage:link
chmod -R 775 storage
chmod -R 775 bootstrap/cache
```

### 6. Setup Python Metadata Server
```bash
cd python-metadata-server
python3 install.py
./start_server.sh
```

### 7. Create Superadmin
```bash
php artisan db:seed --class=SuperAdminSeeder
```

## 🔐 Default Access Credentials

### Superadmin Account
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: Super Administrator
- **Permissions**: Full system access

### Admin Account
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: Administrator
- **Permissions**: Administrative access

### Developer Account
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: Developer
- **Permissions**: App management and development access

> **⚠️ Security Notice**: Change default passwords immediately after installation!

## 🗄️ Database Access

### Database Configuration
The system supports both MySQL and SQLite databases:

#### MySQL Configuration (.env)
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=mobile_apps_management
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

#### SQLite Configuration (.env)
```env
DB_CONNECTION=sqlite
DB_DATABASE=/absolute/path/to/database.sqlite
```

### Database Access Methods

#### 1. Laravel Tinker (Recommended)
```bash
# Access database through Laravel ORM
php artisan tinker

# Example queries
>>> App\Models\User::all()
>>> App\Models\App::where('status', 'active')->count()
>>> App\Models\AuditLog::latest()->take(10)->get()
>>> App\Models\User::where('email', '<EMAIL>')->first()
```

#### 2. Direct Database Access

**MySQL:**
```bash
# Command line access
mysql -u username -p mobile_apps_management

# Show tables
SHOW TABLES;

# View users
SELECT id, name, email, created_at FROM users;

# View apps
SELECT name, version, platform, status, created_at FROM apps;

# View roles and permissions
SELECT r.name as role, p.name as permission
FROM roles r
JOIN role_has_permissions rhp ON r.id = rhp.role_id
JOIN permissions p ON rhp.permission_id = p.id;
```

**SQLite:**
```bash
# Command line access
sqlite3 database/database.sqlite

# Show tables
.tables

# View users
SELECT id, name, email, created_at FROM users;

# View apps with details
SELECT name, version, platform, status, created_at FROM apps;

# Exit SQLite
.quit
```

#### 3. Database GUI Tools

**MySQL:**
- **phpMyAdmin**: Web-based administration
- **MySQL Workbench**: Official MySQL GUI
- **Sequel Pro**: macOS MySQL client
- **DBeaver**: Cross-platform database tool

**SQLite:**
- **DB Browser for SQLite**: User-friendly SQLite GUI
- **SQLiteStudio**: Advanced SQLite manager
- **DBeaver**: Cross-platform support

### Important Database Tables

#### Core Tables
- `users` - User accounts and authentication
- `roles` - User roles (Admin, Manager, Developer, Viewer)
- `permissions` - System permissions
- `model_has_roles` - User-role assignments
- `role_has_permissions` - Role-permission assignments

#### App Management Tables
- `apps` - Mobile applications and metadata
- `sprints` - Development sprints/projects
- `file_access_logs` - Download tracking
- `audit_logs` - System activity logging

#### Security Tables
- `personal_access_tokens` - API tokens
- `storage_cleanup_logs` - File cleanup tracking

### Common Database Queries

#### User Management
```sql
-- View all users with roles
SELECT u.name, u.email, r.name as role
FROM users u
LEFT JOIN model_has_roles mhr ON u.id = mhr.model_id
LEFT JOIN roles r ON mhr.role_id = r.id;

-- Find superadmin users
SELECT u.* FROM users u
JOIN model_has_roles mhr ON u.id = mhr.model_id
JOIN roles r ON mhr.role_id = r.id
WHERE r.name = 'Super Administrator';
```

#### App Statistics
```sql
-- Apps by platform
SELECT platform, COUNT(*) as count FROM apps GROUP BY platform;

-- Apps by status
SELECT status, COUNT(*) as count FROM apps GROUP BY status;

-- Recent uploads
SELECT name, version, platform, created_at
FROM apps
ORDER BY created_at DESC
LIMIT 10;
```

#### Audit Trail
```sql
-- Recent user activities
SELECT event, description, created_at, user_email
FROM audit_logs
ORDER BY created_at DESC
LIMIT 20;

-- Failed login attempts
SELECT * FROM audit_logs
WHERE event = 'login_failed'
ORDER BY created_at DESC;
```

## 🏗️ System Architecture

### Directory Structure
```
mobile-apps-management/
├── app/
│   ├── Http/Controllers/     # Application controllers
│   ├── Models/              # Eloquent models
│   ├── Middleware/          # Custom middleware
│   └── Providers/           # Service providers
├── config/
│   ├── security.php         # Security configuration
│   └── filesystems.php      # Storage configuration
├── database/
│   ├── migrations/          # Database migrations
│   └── seeders/            # Database seeders
├── resources/
│   └── views/              # Blade templates
├── storage/
│   ├── app/private/        # Secure file storage
│   └── logs/               # Application logs
└── public/
    └── storage/            # Public file access
```

### Security Model
```
Request → Authentication → Authorization → Permission Check → Action
```

### File Storage Structure
```
storage/app/private/
├── apps/
│   ├── ios/YYYY/MM/        # iOS app files
│   ├── android/YYYY/MM/    # Android app files
│   └── huawei/YYYY/MM/     # Huawei app files
├── icons/YYYY/MM/          # App icons
└── qr-codes/YYYY/MM/       # QR codes
```

## 🔧 Configuration

### Security Configuration
Edit `config/security.php`:

```php
'upload_security' => [
    'quarantine_duration' => 300,        // 5 minutes
    'max_file_size' => '500MB',
    'allowed_extensions' => ['ipa', 'apk'],
    'virus_scanning' => false,
],

'file_access' => [
    'token_expiry' => 3600,              // 1 hour
    'max_downloads_per_token' => 1,
    'require_authentication' => true,
],
```

### Storage Configuration
Edit `config/filesystems.php`:

```php
'disks' => [
    'private' => [
        'driver' => 'local',
        'root' => storage_path('app/private'),
        'visibility' => 'private',
    ],
],
```

## 📊 Monitoring and Maintenance

### Log Files
- **Application Logs**: `storage/logs/laravel.log`
- **Audit Logs**: Database table `audit_logs`
- **File Access Logs**: Database table `file_access_logs`

### Health Checks
```bash
# Check system status
php artisan about

# Check storage permissions
ls -la storage/

# View recent logs
tail -f storage/logs/laravel.log
```

### Maintenance Commands
```bash
# Clear caches
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# Optimize for production
php artisan optimize

# Database maintenance
php artisan migrate:status
```

## 🚀 Deployment

### Production Checklist
- [ ] Change default passwords
- [ ] Configure proper database
- [ ] Set up SSL/TLS certificates
- [ ] Configure file upload limits
- [ ] Set up backup procedures
- [ ] Configure monitoring
- [ ] Review security settings
- [ ] Test all functionality

### Environment Variables
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Security
SESSION_SECURE_COOKIE=true
SANCTUM_STATEFUL_DOMAINS=your-domain.com
```

## 📞 Support

### Troubleshooting
1. **Upload Issues**: Check PHP upload limits
2. **Permission Errors**: Verify storage permissions
3. **Database Issues**: Check connection settings
4. **Authentication Problems**: Clear sessions and cache

### Common Issues
- **File too large**: Increase PHP upload limits
- **Permission denied**: Check file/folder permissions
- **Database connection**: Verify credentials and server status
- **Metadata extraction fails**: Check file format and size

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Built with ❤️ using Laravel 12**
