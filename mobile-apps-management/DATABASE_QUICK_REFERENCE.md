# Database Quick Reference - Mobile Apps Management

## 🚀 Quick Access Commands

### <PERSON><PERSON>ker (Recommended)
```bash
# Start Tinker
php artisan tinker

# Exit Tinker
>>> exit
```

### MySQL Direct Access
```bash
# Connect to database
mysql -u username -p mobile_apps_management

# Exit MySQL
mysql> exit;
```

### SQLite Direct Access
```bash
# Connect to SQLite database
sqlite3 database/database.sqlite

# Exit SQLite
.quit
```

## 📊 Essential Queries

### User Management
```sql
-- View all users with roles
SELECT u.id, u.name, u.email, r.name as role, u.created_at
FROM users u 
LEFT JOIN model_has_roles mhr ON u.id = mhr.model_id 
LEFT JOIN roles r ON mhr.role_id = r.id
ORDER BY u.created_at DESC;

-- Find superadmin users
SELECT u.name, u.email, u.created_at
FROM users u 
JOIN model_has_roles mhr ON u.id = mhr.model_id 
JOIN roles r ON mhr.role_id = r.id 
WHERE r.name = 'Super Administrator';

-- Count users by role
SELECT r.name as role, COUNT(*) as user_count
FROM roles r
LEFT JOIN model_has_roles mhr ON r.id = mhr.role_id
GROUP BY r.id, r.name;
```

### App Statistics
```sql
-- Apps overview
SELECT 
    platform,
    status,
    COUNT(*) as count,
    SUM(file_size) as total_size
FROM apps 
GROUP BY platform, status;

-- Recent uploads
SELECT name, version, platform, status, created_at
FROM apps 
ORDER BY created_at DESC 
LIMIT 10;

-- Most downloaded apps
SELECT name, version, platform, download_count
FROM apps 
WHERE download_count > 0
ORDER BY download_count DESC 
LIMIT 10;

-- Apps in quarantine
SELECT name, version, platform, quarantine_until
FROM apps 
WHERE status = 'quarantined'
ORDER BY created_at DESC;
```

### Security & Audit
```sql
-- Recent login attempts
SELECT user_email, event, created_at, ip_address
FROM audit_logs 
WHERE event IN ('login', 'login_failed')
ORDER BY created_at DESC 
LIMIT 20;

-- Failed login attempts (last 24 hours)
SELECT user_email, COUNT(*) as attempts, MAX(created_at) as last_attempt
FROM audit_logs 
WHERE event = 'login_failed' 
AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY user_email
ORDER BY attempts DESC;

-- File access logs
SELECT app_id, user_id, success, created_at
FROM file_access_logs 
ORDER BY created_at DESC 
LIMIT 20;
```

### Sprint Management
```sql
-- Sprints with app counts
SELECT s.name, s.status, COUNT(a.id) as app_count
FROM sprints s
LEFT JOIN apps a ON s.id = a.sprint_id
GROUP BY s.id, s.name, s.status
ORDER BY s.created_at DESC;

-- Active sprints
SELECT id, name, description, start_date, end_date
FROM sprints 
WHERE status = 'active'
ORDER BY start_date;
```

## 🔧 Laravel Tinker Commands

### User Operations
```php
// Get all users
User::all()

// Find user by email
User::where('email', '<EMAIL>')->first()

// Create new user
$user = User::create([
    'name' => 'New User',
    'email' => '<EMAIL>',
    'password' => bcrypt('password123'),
    'email_verified_at' => now()
]);

// Assign role to user
$user->assignRole('Developer');

// Get user's roles
$user->roles

// Get user's permissions
$user->getAllPermissions()
```

### App Operations
```php
// Get all apps
App::all()

// Apps by platform
App::where('platform', 'ios')->get()

// Apps in quarantine
App::where('status', 'quarantined')->get()

// Recent apps
App::latest()->take(10)->get()

// App with relationships
App::with(['sprint', 'uploader'])->find(1)
```

### Role & Permission Operations
```php
// Get all roles
Role::all()

// Get all permissions
Permission::all()

// Users with specific role
User::role('Super Administrator')->get()

// Create new role
Role::create(['name' => 'Custom Role'])

// Assign permission to role
$role = Role::findByName('Developer');
$role->givePermissionTo('apps.create');
```

### Audit Log Operations
```php
// Recent audit logs
AuditLog::latest()->take(20)->get()

// Logs by user
AuditLog::where('user_email', '<EMAIL>')->get()

// Logs by event type
AuditLog::where('event', 'login')->latest()->get()

// Failed login attempts
AuditLog::where('event', 'login_failed')
    ->where('created_at', '>=', now()->subDay())
    ->get()
```

## 🗂️ Database Schema Reference

### Core Tables
```sql
-- users: User accounts
id, name, email, password, email_verified_at, created_at, updated_at

-- roles: System roles
id, name, guard_name, created_at, updated_at

-- permissions: System permissions
id, name, guard_name, created_at, updated_at

-- model_has_roles: User-role assignments
role_id, model_type, model_id

-- role_has_permissions: Role-permission assignments
permission_id, role_id
```

### App Management Tables
```sql
-- apps: Mobile applications
id, name, version, build_number, bundle_identifier, platform, 
file_path, file_hash, file_size, file_name, mime_type, 
icon_path, qr_code_path, changelog, ticket_link, metadata,
sprint_id, uploaded_by, released_by, status, quarantine_until,
access_token, download_count, created_at, updated_at

-- sprints: Development sprints
id, name, description, start_date, end_date, status, 
created_by, created_at, updated_at

-- file_access_logs: Download tracking
id, app_id, user_id, access_token, success, error_message,
ip_address, user_agent, platform_detected, created_at

-- audit_logs: System activity
id, event, description, user_id, user_email, ip_address,
user_agent, created_at
```

## 🔍 Troubleshooting Queries

### Find Issues
```sql
-- Users without roles
SELECT u.* FROM users u 
LEFT JOIN model_has_roles mhr ON u.id = mhr.model_id 
WHERE mhr.model_id IS NULL;

-- Apps with missing files
SELECT * FROM apps 
WHERE file_path IS NULL OR file_path = '';

-- Orphaned file access logs
SELECT fal.* FROM file_access_logs fal
LEFT JOIN apps a ON fal.app_id = a.id
WHERE a.id IS NULL;

-- Large files (> 100MB)
SELECT name, version, platform, file_size 
FROM apps 
WHERE file_size > 104857600
ORDER BY file_size DESC;
```

### Performance Queries
```sql
-- Database size (MySQL)
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'mobile_apps_management'
ORDER BY (data_length + index_length) DESC;

-- Record counts
SELECT 'users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'apps', COUNT(*) FROM apps
UNION ALL
SELECT 'audit_logs', COUNT(*) FROM audit_logs
UNION ALL
SELECT 'file_access_logs', COUNT(*) FROM file_access_logs;
```

## 🛠️ Maintenance Commands

### Database Maintenance
```bash
# Check database connection
php artisan db:show

# Run migrations
php artisan migrate:status
php artisan migrate

# Seed database
php artisan db:seed

# Fresh migration (WARNING: Destroys data)
php artisan migrate:fresh --seed
```

### Backup & Restore
```bash
# MySQL backup
mysqldump -u username -p mobile_apps_management > backup.sql

# MySQL restore
mysql -u username -p mobile_apps_management < backup.sql

# SQLite backup
cp database/database.sqlite database/backup_$(date +%Y%m%d).sqlite

# SQLite restore
cp database/backup_20231201.sqlite database/database.sqlite
```

## 📞 Emergency Access

### Reset Superadmin Password
```sql
-- Reset to 'password'
UPDATE users 
SET password = '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' 
WHERE email = '<EMAIL>';
```

### Create Emergency Admin
```php
// Via Tinker
$user = User::create([
    'name' => 'Emergency Admin',
    'email' => '<EMAIL>',
    'password' => bcrypt('EmergencyPass123!'),
    'email_verified_at' => now()
]);
$user->assignRole('Super Administrator');
```

---

**Keep this reference secure and accessible only to authorized database administrators.**
