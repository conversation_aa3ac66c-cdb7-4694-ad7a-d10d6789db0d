# Administrator Guide - Mobile Apps Management System

## 🔐 Superadmin Access

### Default System Accounts

#### Superadmin Account
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: Super Administrator
- **Permissions**: Full system access

#### Admin Account
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: Administrator
- **Permissions**: Administrative access

#### Developer Account
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: Developer
- **Permissions**: App management and development access

### Superadmin Capabilities
The superadmin account has unrestricted access to all system functions:

#### User Management
- Create, edit, and delete user accounts
- Assign and modify user roles
- Reset user passwords
- View user activity logs
- Manage user permissions

#### System Administration
- Access all apps regardless of sprint assignment
- Manage system-wide settings
- View comprehensive audit logs
- Perform system maintenance tasks
- Configure security settings

#### App Management
- Upload apps to any sprint
- Release apps from quarantine immediately
- Delete any app from the system
- Regenerate access tokens
- View detailed download statistics

### Creating Additional Superadmins

#### Method 1: Through Web Interface
1. Login as existing superadmin
2. Navigate to **Users** → **Create User**
3. Fill in user details
4. Assign **Super Administrator** role
5. User will receive login credentials

#### Method 2: Using Artisan Command
```bash
php artisan tinker

# Create new superadmin user
>>> $user = App\Models\User::create([
...     'name' => 'New Admin',
...     'email' => '<EMAIL>',
...     'password' => bcrypt('SecurePassword123!'),
...     'email_verified_at' => now()
... ]);

# Assign superadmin role
>>> $user->assignRole('Super Administrator');

# Verify role assignment
>>> $user->roles;
```

#### Method 3: Database Direct Access
```sql
-- Insert new user
INSERT INTO users (name, email, password, email_verified_at, created_at, updated_at) 
VALUES ('Admin Name', '<EMAIL>', '$2y$12$hashed_password', NOW(), NOW(), NOW());

-- Get the user ID
SELECT id FROM users WHERE email = '<EMAIL>';

-- Assign superadmin role (role_id = 1 for Super Administrator)
INSERT INTO model_has_roles (role_id, model_type, model_id) 
VALUES (1, 'App\\Models\\User', [USER_ID]);
```

## 🛡️ Security Management

### Password Policies
- Minimum 8 characters
- Must contain uppercase, lowercase, numbers
- Special characters recommended
- Regular password changes enforced

### Session Management
- Session timeout: 2 hours of inactivity
- Concurrent session limit: 3 per user
- Secure cookie settings in production
- CSRF protection on all forms

### File Security
- All uploads quarantined for 5 minutes
- SHA-256 integrity verification
- Virus scanning (configurable)
- Secure token-based downloads

### Audit Logging
All administrative actions are logged:
- User login/logout attempts
- Permission changes
- App uploads/downloads
- System configuration changes
- Failed access attempts

## 📊 System Monitoring

### Key Metrics to Monitor
- **User Activity**: Login frequency, failed attempts
- **App Usage**: Upload/download statistics
- **Storage**: Disk usage, file counts
- **Security**: Quarantine status, access violations
- **Performance**: Response times, error rates

### Database Monitoring Queries

#### User Statistics
```sql
-- Active users in last 30 days
SELECT COUNT(DISTINCT user_id) as active_users 
FROM audit_logs 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- User login frequency
SELECT user_email, COUNT(*) as login_count 
FROM audit_logs 
WHERE event = 'login' 
AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
GROUP BY user_email 
ORDER BY login_count DESC;
```

#### App Statistics
```sql
-- Storage usage by platform
SELECT platform, 
       COUNT(*) as app_count,
       SUM(file_size) as total_size,
       AVG(file_size) as avg_size
FROM apps 
GROUP BY platform;

-- Most downloaded apps
SELECT name, version, platform, download_count 
FROM apps 
ORDER BY download_count DESC 
LIMIT 10;
```

#### Security Monitoring
```sql
-- Failed login attempts
SELECT user_email, COUNT(*) as failed_attempts, MAX(created_at) as last_attempt
FROM audit_logs 
WHERE event = 'login_failed' 
AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY user_email 
HAVING failed_attempts > 3;

-- Apps in quarantine
SELECT name, version, platform, quarantine_until 
FROM apps 
WHERE status = 'quarantined' 
ORDER BY created_at DESC;
```

## 🔧 System Maintenance

### Regular Maintenance Tasks

#### Daily
- Review audit logs for security issues
- Check quarantined apps
- Monitor storage usage
- Verify backup completion

#### Weekly
- Review user access and permissions
- Clean up old log files
- Update system dependencies
- Performance monitoring

#### Monthly
- User access review
- Security policy updates
- System backup verification
- Capacity planning

### Maintenance Commands

#### Cache Management
```bash
# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan view:clear
php artisan route:clear

# Rebuild caches for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

#### Database Maintenance
```bash
# Check database status
php artisan db:show

# Run migrations
php artisan migrate:status
php artisan migrate

# Optimize database
php artisan db:seed --class=OptimizeTablesSeeder
```

#### Storage Cleanup
```bash
# Clear temporary files
php artisan storage:cleanup

# Optimize storage
php artisan storage:optimize

# Check storage permissions
ls -la storage/
```

## 🚨 Emergency Procedures

### Lost Superadmin Access

#### Option 1: Create Emergency Admin via Artisan
```bash
php artisan tinker

>>> $user = App\Models\User::create([
...     'name' => 'Emergency Admin',
...     'email' => '<EMAIL>',
...     'password' => bcrypt('EmergencyPass123!'),
...     'email_verified_at' => now()
... ]);
>>> $user->assignRole('Super Administrator');
```

#### Option 2: Direct Database Access
```sql
-- Reset existing admin password
UPDATE users 
SET password = '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' 
WHERE email = '<EMAIL>';
-- Password is 'password'
```

### System Recovery

#### Database Corruption
1. Stop the application
2. Restore from latest backup
3. Run integrity checks
4. Restart services
5. Verify functionality

#### File System Issues
1. Check storage permissions
2. Verify disk space
3. Restore files from backup
4. Rebuild file indexes
5. Test upload/download

### Security Incidents

#### Suspected Breach
1. Change all admin passwords immediately
2. Review audit logs for suspicious activity
3. Check file integrity
4. Disable affected user accounts
5. Generate new access tokens
6. Document incident

#### Malicious File Upload
1. Quarantine suspicious files
2. Run virus scan
3. Check upload logs
4. Trace user activity
5. Remove malicious content
6. Update security rules

## 📋 Configuration Reference

### Environment Variables
```env
# Application
APP_NAME="Mobile Apps Management"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=mobile_apps_management
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Security
SESSION_LIFETIME=120
SESSION_SECURE_COOKIE=true
SANCTUM_STATEFUL_DOMAINS=your-domain.com

# File Storage
FILESYSTEM_DISK=private
MAX_FILE_SIZE=500MB
QUARANTINE_DURATION=300

# Mail (for notifications)
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-server
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
```

### Security Configuration
```php
// config/security.php
return [
    'upload_security' => [
        'quarantine_duration' => env('QUARANTINE_DURATION', 300),
        'max_file_size' => env('MAX_FILE_SIZE', '500MB'),
        'allowed_extensions' => ['ipa', 'apk'],
        'virus_scanning' => env('VIRUS_SCANNING', false),
        'auto_release' => env('AUTO_RELEASE_QUARANTINE', false),
    ],
    
    'file_access' => [
        'token_expiry' => env('FILE_TOKEN_EXPIRY', 3600),
        'max_downloads_per_token' => env('MAX_DOWNLOADS_PER_TOKEN', 1),
        'require_authentication' => env('REQUIRE_AUTH_DOWNLOAD', true),
        'log_all_access' => env('LOG_ALL_FILE_ACCESS', true),
    ],
    
    'audit_logging' => [
        'enabled' => env('AUDIT_LOGGING', true),
        'log_level' => env('AUDIT_LOG_LEVEL', 'info'),
        'retention_days' => env('AUDIT_RETENTION_DAYS', 90),
    ],
];
```

## 📞 Support Contacts

### Technical Issues
- **System Administrator**: <EMAIL>
- **Database Issues**: <EMAIL>
- **Security Concerns**: <EMAIL>

### Emergency Contacts
- **24/7 Support**: +1-xxx-xxx-xxxx
- **Security Hotline**: +1-xxx-xxx-xxxx
- **Management Escalation**: <EMAIL>

---

**This guide should be kept secure and accessible only to authorized administrators.**
