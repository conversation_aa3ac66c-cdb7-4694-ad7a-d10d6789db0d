@extends('layouts.app')

@section('title', 'File Storage Management')
@section('page-title', 'Secure File Storage Management')

@section('content')
<div class="row">
    <!-- Storage Statistics -->
    <div class="col-12 mb-4">
        <div class="row">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fas fa-file-archive fa-2x text-primary me-3"></i>
                            <div>
                                <h3 class="mb-0" id="totalFiles">-</h3>
                                <small class="text-muted">Total Files</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fas fa-hdd fa-2x text-info me-3"></i>
                            <div>
                                <h3 class="mb-0" id="totalSize">-</h3>
                                <small class="text-muted">Storage Used</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fas fa-shield-alt fa-2x text-warning me-3"></i>
                            <div>
                                <h3 class="mb-0" id="quarantinedFiles">-</h3>
                                <small class="text-muted">Quarantined</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fas fa-download fa-2x text-success me-3"></i>
                            <div>
                                <h3 class="mb-0" id="totalDownloads">-</h3>
                                <small class="text-muted">Downloads</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- File Upload Section -->
    <div class="col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0">
                    <i class="fas fa-cloud-upload-alt me-2 text-primary"></i>
                    Secure File Upload
                </h5>
            </div>
            <div class="card-body">
                @can('apps.upload')
                <form id="uploadForm" enctype="multipart/form-data">
                    @csrf
                    <div class="mb-3">
                        <label for="app_name" class="form-label">App Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="app_name" name="app_name" required>
                    </div>

                    <div class="mb-3">
                        <label for="version" class="form-label">Version <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="version" name="version" placeholder="e.g., 1.0.0" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="file" class="form-label">App File <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="file" name="file" accept=".apk,.ipa" required>
                        <div class="form-text">
                            Allowed formats: APK (Android), IPA (iOS). Max size: {{ config('security.upload_security.max_file_size', '500MB') }}
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" id="uploadBtn">
                            <i class="fas fa-upload me-2"></i>
                            <span class="btn-text">Upload File</span>
                            <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                        </button>
                    </div>
                </form>
                @else
                <div class="text-center py-4">
                    <i class="fas fa-lock fa-3x text-muted mb-3"></i>
                    <p class="text-muted">You don't have permission to upload files.</p>
                </div>
                @endcan
            </div>
        </div>
    </div>

    <!-- Storage Analytics -->
    <div class="col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2 text-primary"></i>
                    Storage Analytics
                </h5>
            </div>
            <div class="card-body">
                <div id="platformChart" style="height: 300px;">
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status"></div>
                            <p class="mt-2 text-muted">Loading analytics...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Uploads -->
    <div class="col-12 mt-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2 text-primary"></i>
                    Recent Uploads
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>App Name</th>
                                <th>Version + Build</th>
                                <th>Platform</th>
                                <th>Sprint</th>
                                <th>Size</th>
                                <th>Status</th>
                                <th>Uploaded By</th>
                                <th>Upload Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="recentUploadsTable">
                            <tr>
                                <td colspan="8" class="text-center">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p class="mt-2 text-muted">Loading recent uploads...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Monitoring -->
    <div class="col-12 mt-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2 text-primary"></i>
                        Security Monitoring
                    </h5>
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshSecurityData()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="border rounded p-3 text-center">
                            <h6 class="text-muted">Failed Downloads</h6>
                            <h4 class="text-danger" id="failedDownloads">-</h4>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border rounded p-3 text-center">
                            <h6 class="text-muted">Blocked IPs</h6>
                            <h4 class="text-warning" id="blockedIPs">-</h4>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border rounded p-3 text-center">
                            <h6 class="text-muted">Security Events</h6>
                            <h4 class="text-info" id="securityEvents">-</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload Progress Modal -->
<div class="modal fade" id="uploadProgressModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>Uploading File
                </h5>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar"
                             style="width: 0%"
                             id="uploadProgress">0%</div>
                    </div>
                </div>
                <div class="text-center">
                    <p class="mb-0" id="uploadStatus">Preparing upload...</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadStorageStats();

    // File upload form
    const uploadForm = document.getElementById('uploadForm');
    if (uploadForm) {
        uploadForm.addEventListener('submit', handleFileUpload);
    }
});

function loadStorageStats() {
    fetch('/files/storage-stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('totalFiles').textContent = data.total_files || 0;
            document.getElementById('totalSize').textContent = formatFileSize(data.total_size || 0);
            document.getElementById('quarantinedFiles').textContent = data.quarantined_files || 0;
            document.getElementById('totalDownloads').textContent = data.total_downloads || 0;
            document.getElementById('failedDownloads').textContent = data.failed_downloads || 0;

            // Update recent uploads table
            updateRecentUploadsTable(data.recent_uploads || []);

            // Update platform chart
            updatePlatformChart(data.storage_usage_by_platform || []);
        })
        .catch(error => {
            console.error('Error loading storage stats:', error);
            showAlert('danger', 'Failed to load storage statistics');
        });
}

function handleFileUpload(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const uploadBtn = document.getElementById('uploadBtn');
    const progressModal = new bootstrap.Modal(document.getElementById('uploadProgressModal'));

    // Show progress modal
    progressModal.show();

    // Disable upload button
    uploadBtn.disabled = true;
    uploadBtn.querySelector('.btn-text').textContent = 'Uploading...';
    uploadBtn.querySelector('.spinner-border').classList.remove('d-none');

    // Create XMLHttpRequest for progress tracking
    const xhr = new XMLHttpRequest();

    xhr.upload.addEventListener('progress', function(e) {
        if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            const progressBar = document.getElementById('uploadProgress');
            progressBar.style.width = percentComplete + '%';
            progressBar.textContent = Math.round(percentComplete) + '%';

            document.getElementById('uploadStatus').textContent =
                `Uploading... ${Math.round(percentComplete)}%`;
        }
    });

    xhr.addEventListener('load', function() {
        progressModal.hide();

        if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
                showAlert('success', response.message);
                uploadForm.reset();
                loadStorageStats(); // Refresh stats
            } else {
                showAlert('danger', response.message);
            }
        } else {
            showAlert('danger', 'Upload failed. Please try again.');
        }

        // Reset upload button
        uploadBtn.disabled = false;
        uploadBtn.querySelector('.btn-text').textContent = 'Upload File';
        uploadBtn.querySelector('.spinner-border').classList.add('d-none');
    });

    xhr.addEventListener('error', function() {
        progressModal.hide();
        showAlert('danger', 'Upload failed. Please check your connection and try again.');

        // Reset upload button
        uploadBtn.disabled = false;
        uploadBtn.querySelector('.btn-text').textContent = 'Upload File';
        uploadBtn.querySelector('.spinner-border').classList.add('d-none');
    });

    xhr.open('POST', '/files/upload');
    xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
    xhr.send(formData);
}

function updateRecentUploadsTable(uploads) {
    const tbody = document.getElementById('recentUploadsTable');

    if (uploads.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">No recent uploads</td></tr>';
        return;
    }

    tbody.innerHTML = uploads.map(upload => `
        <tr class="cursor-pointer" onclick="window.location.href='/apps/${upload.id}'">
            <td>
                <div class="d-flex align-items-center">
                    ${upload.icon_path ?
                        `<img src="/storage/${upload.icon_path}" alt="${upload.name}" class="rounded me-2" style="width: 24px; height: 24px;">` :
                        `<i class="fab fa-${upload.platform === 'android' ? 'android' : 'apple'} me-2"></i>`
                    }
                    <strong>${upload.name}</strong>
                </div>
            </td>
            <td>
                <div>
                    <strong>${upload.version}</strong>
                    ${upload.build_number ? `<small class="text-muted">(${upload.build_number})</small>` : ''}
                </div>
            </td>
            <td>
                <span class="badge bg-${upload.platform === 'android' ? 'success' : (upload.platform === 'huawei' ? 'warning' : 'primary')}">
                    <i class="fab fa-${upload.platform === 'android' ? 'android' : (upload.platform === 'huawei' ? 'android' : 'apple')} me-1"></i>
                    ${upload.platform.toUpperCase()}
                </span>
            </td>
            <td>
                ${upload.sprint ?
                    `<span class="badge bg-info">${upload.sprint.name}</span>` :
                    '<span class="text-muted">No Sprint</span>'
                }
            </td>
            <td>${formatFileSize(upload.file_size)}</td>
            <td>
                <span class="badge bg-${upload.status === 'active' ? 'success' : 'warning'}">
                    ${upload.status.toUpperCase()}
                </span>
            </td>
            <td>${upload.uploader?.name || 'Unknown'}</td>
            <td>${new Date(upload.created_at).toLocaleDateString()}</td>
            <td>
                <a href="/apps/${upload.id}" class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation()">
                    <i class="fas fa-eye"></i>
                </a>
            </td>
        </tr>
    `).join('');
}

function updatePlatformChart(platformData) {
    const chartContainer = document.getElementById('platformChart');

    if (platformData.length === 0) {
        chartContainer.innerHTML = '<div class="text-center text-muted py-5">No data available</div>';
        return;
    }

    // Simple chart representation (you can integrate Chart.js here)
    const total = platformData.reduce((sum, item) => sum + item.count, 0);

    chartContainer.innerHTML = platformData.map(item => {
        const percentage = ((item.count / total) * 100).toFixed(1);
        return `
            <div class="mb-3">
                <div class="d-flex justify-content-between">
                    <span>${item.platform.toUpperCase()}</span>
                    <span>${item.count} files (${percentage}%)</span>
                </div>
                <div class="progress">
                    <div class="progress-bar bg-${item.platform === 'android' ? 'success' : 'primary'}"
                         style="width: ${percentage}%"></div>
                </div>
            </div>
        `;
    }).join('');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function releaseFromQuarantine(appId) {
    if (confirm('Release this app from quarantine?')) {
        fetch(`/files/${appId}/release-quarantine`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                loadStorageStats();
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Failed to release from quarantine');
        });
    }
}

function generateDownloadToken(appId) {
    fetch(`/files/${appId}/generate-token`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.open(data.download_url, '_blank');
            showAlert('success', 'Download started');
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Failed to generate download token');
    });
}

function refreshSecurityData() {
    loadStorageStats();
    showAlert('info', 'Security data refreshed');
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.querySelector('.row').insertAdjacentHTML('afterbegin', alertHtml);
}
</script>
@endpush
