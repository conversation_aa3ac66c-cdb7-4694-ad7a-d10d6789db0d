@extends('layouts.app')

@section('title', 'Upload App')
@section('page-title', 'Upload New App')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-upload me-2 text-primary"></i>
                        Upload New App
                    </h5>
                    <a href="{{ route('apps.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Apps
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('apps.store') }}" enctype="multipart/form-data" id="uploadAppForm">
                    @csrf

                    <div class="row">
                        <div class="col-md-8">
                            <!-- App Information -->
                            <div class="card border">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>App Information
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <!-- Sprint -->
                                    <div class="mb-3">
                                        <label for="sprint_id" class="form-label">
                                            Sprint <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select @error('sprint_id') is-invalid @enderror"
                                                id="sprint_id"
                                                name="sprint_id"
                                                required>
                                            <option value="">Select Sprint</option>
                                            @foreach($sprints as $sprint)
                                            <option value="{{ $sprint->id }}" {{ old('sprint_id') == $sprint->id ? 'selected' : '' }}>
                                                {{ $sprint->name }}
                                            </option>
                                            @endforeach
                                        </select>
                                        @error('sprint_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">
                                            Select the development sprint for this app
                                        </div>
                                    </div>

                                    <!-- File Upload -->
                                    <div class="mb-3">
                                        <label for="file" class="form-label">
                                            App File <span class="text-danger">*</span>
                                        </label>
                                        <input type="file"
                                               class="form-control @error('file') is-invalid @enderror"
                                               id="file"
                                               name="file"
                                               accept=".apk,.ipa"
                                               required>
                                        @error('file')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror

                                        <!-- Upload Progress -->
                                        <div id="uploadProgress" class="mt-2 d-none">
                                            <div class="d-flex justify-content-between align-items-center mb-1">
                                                <small class="text-muted">Uploading...</small>
                                                <small class="text-muted" id="uploadPercentage">0%</small>
                                            </div>
                                            <div class="progress" style="height: 6px;">
                                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                                     role="progressbar"
                                                     style="width: 0%"
                                                     id="uploadProgressBar"></div>
                                            </div>
                                        </div>

                                        <!-- Upload Status -->
                                        <div id="uploadStatus" class="mt-2 d-none">
                                            <div class="alert alert-success d-flex justify-content-between align-items-center">
                                                <span>
                                                    <i class="fas fa-check-circle me-2"></i>
                                                    File uploaded successfully to temporary storage
                                                </span>
                                                <button type="button" class="btn btn-primary btn-sm" id="retrieveMetadataBtn">
                                                    <i class="fas fa-download me-1"></i>Retrieve Metadata
                                                </button>
                                            </div>
                                        </div>

                                        <div class="form-text" id="fileHelp">
                                            Select your app file. Supported formats: IPA (iOS), APK (Android/Huawei).
                                            Max size: 200MB per file
                                            <br><small class="text-muted">
                                                <i class="fas fa-info-circle me-1"></i>
                                                File will be uploaded to temporary storage, then you can retrieve metadata.
                                            </small>
                                        </div>
                                    </div>

                                    <!-- Changelog -->
                                    <div class="mb-3">
                                        <label for="changelog" class="form-label">
                                            Changelog <span class="text-danger">*</span>
                                        </label>
                                        <textarea class="form-control @error('changelog') is-invalid @enderror"
                                                  id="changelog"
                                                  name="changelog"
                                                  rows="4"
                                                  placeholder="What's new in this version?"
                                                  required>{{ old('changelog') }}</textarea>
                                        @error('changelog')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">
                                            <span class="text-danger">Required:</span> Describe the changes and new features in this version
                                        </div>
                                    </div>

                                    <!-- Ticket Links -->
                                    <div class="mb-3">
                                        <label class="form-label">
                                            Ticket/Issue Links
                                        </label>
                                        <div id="ticketLinksContainer">
                                            <div class="ticket-link-row mb-2">
                                                <div class="input-group">
                                                    <input type="url"
                                                           class="form-control @error('ticket_links.0') is-invalid @enderror"
                                                           name="ticket_links[]"
                                                           value="{{ old('ticket_links.0') }}"
                                                           placeholder="https://jira.company.com/ticket/123">
                                                    <button type="button" class="btn btn-outline-success" onclick="addTicketLink()">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                </div>
                                                @error('ticket_links.0')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="form-text">
                                            Add links to related tickets, issues, or documentation
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Upload Summary -->
                            <div class="card border">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>Upload Summary
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div id="uploadSummary">
                                        <!-- File Information -->
                                        <div class="mb-3">
                                            <strong>Selected File:</strong>
                                            <div id="selectedFile" class="text-muted small">No file selected</div>
                                        </div>
                                        <div class="mb-3">
                                            <strong>File Size:</strong>
                                            <div id="fileSize" class="text-muted small">-</div>
                                        </div>

                                        <!-- Extracted Information (will be populated after metadata retrieval) -->
                                        <div id="extractedInfo" class="d-none">
                                            <hr>
                                            <h6 class="text-primary mb-3">
                                                <i class="fas fa-magic me-1"></i>App Information
                                            </h6>

                                            <!-- App Icon -->
                                            <div class="text-center mb-3">
                                                <img id="appIcon"
                                                     src=""
                                                     alt="App Icon"
                                                     class="rounded-3 border d-none"
                                                     style="width: 80px; height: 80px; object-fit: cover;">
                                                <div id="appIconPlaceholder" class="d-inline-flex align-items-center justify-content-center rounded-3 border bg-light"
                                                     style="width: 80px; height: 80px;">
                                                    <i class="fas fa-mobile-alt text-muted fa-2x"></i>
                                                </div>
                                            </div>

                                            <div class="mb-2">
                                                <strong>App Name:</strong>
                                                <div id="extractedAppName" class="text-muted small">-</div>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Bundle Identifier:</strong>
                                                <div id="extractedBundleId" class="text-muted small">-</div>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Version:</strong>
                                                <div id="extractedVersion" class="text-muted small">-</div>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Build Number:</strong>
                                                <div id="extractedBuildNumber" class="text-muted small">-</div>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Platform:</strong>
                                                <div id="extractedPlatform" class="text-muted small">-</div>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Min OS Version:</strong>
                                                <div id="extractedMinOS" class="text-muted small">-</div>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Executable:</strong>
                                                <div id="extractedExecutable" class="text-muted small">-</div>
                                            </div>
                                            <div class="mb-2">
                                                <strong>URL Schemes:</strong>
                                                <div id="extractedUrlSchemes" class="text-muted small">-</div>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Permissions:</strong>
                                                <div id="extractedPermissions" class="text-muted small">-</div>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Supported Platforms:</strong>
                                                <div id="extractedSupportedPlatforms" class="text-muted small">-</div>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Device Family:</strong>
                                                <div id="extractedDeviceFamily" class="text-muted small">-</div>
                                            </div>
                                        </div>

                                        <!-- Raw Plist Data Section -->
                                        <div class="mt-3">
                                            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#rawPlistData" aria-expanded="false" aria-controls="rawPlistData">
                                                <i class="fas fa-code"></i> Show Raw Plist Data
                                            </button>
                                            <div class="collapse mt-2" id="rawPlistData">
                                                <div class="card card-body">
                                                    <h6>Raw Info.plist Data:</h6>
                                                    <pre id="rawPlistContent" class="small text-muted" style="max-height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 4px;">No data available</pre>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- User Selections -->
                                        <hr>
                                        <h6 class="text-secondary mb-2">
                                            <i class="fas fa-user-edit me-1"></i>Your Selections
                                        </h6>
                                        <div class="mb-2">
                                            <strong>Sprint:</strong>
                                            <div id="selectedSprint" class="text-muted small">Not selected</div>
                                        </div>
                                        <div class="mb-2">
                                            <strong>Changelog:</strong>
                                            <div id="changelogSummary" class="text-muted small">Not provided</div>
                                        </div>
                                        <div class="mb-2">
                                            <strong>Ticket Links:</strong>
                                            <div id="ticketLinksSummary" class="text-muted small">None added</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Security Notice -->
                            <div class="card border-warning mt-3">
                                <div class="card-header bg-warning bg-opacity-10">
                                    <h6 class="mb-0 text-warning">
                                        <i class="fas fa-shield-alt me-2"></i>Security Notice
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="small mb-2">
                                        <i class="fas fa-info-circle me-2 text-info"></i>
                                        Your app will be placed in <strong>quarantine</strong> for security review.
                                    </p>
                                    <p class="small mb-2">
                                        <i class="fas fa-clock me-2 text-warning"></i>
                                        Quarantine period: {{ config('security.upload_security.quarantine_duration', 300) / 60 }} minutes
                                    </p>
                                    <p class="small mb-0">
                                        <i class="fas fa-check me-2 text-success"></i>
                                        After review, the app will be available for download.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ route('apps.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                            <i class="fas fa-upload me-2"></i>
                            <span class="btn-text">Upload App</span>
                            <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                        </button>
                        <small class="text-muted ms-2" id="submitBtnHelp">
                            Complete file upload and metadata retrieval to enable
                        </small>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Upload Progress Modal -->
<div class="modal fade" id="uploadProgressModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>Uploading App
                </h5>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar"
                             style="width: 0%"
                             id="uploadProgress">0%</div>
                    </div>
                </div>
                <div class="text-center">
                    <p class="mb-0" id="uploadStatus">Preparing upload...</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('uploadAppForm');
    const fileInput = document.getElementById('file');
    const sprintSelect = document.getElementById('sprint_id');
    const changelogTextarea = document.getElementById('changelog');
    const submitBtn = document.getElementById('submitBtn');
    const retrieveBtn = document.getElementById('retrieveMetadataBtn');

    // State management
    let fileUploaded = false;
    let metadataRetrieved = false;
    let tempFileId = null;

    // Update summary when form changes
    function updateUploadSummary() {
        const file = fileInput.files[0];
        const sprint = sprintSelect.options[sprintSelect.selectedIndex];
        const changelog = changelogTextarea.value.trim();

        // Update selected file display only (don't trigger upload)
        if (file) {
            document.getElementById('selectedFile').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
        } else {
            document.getElementById('selectedFile').textContent = 'No file selected';
            document.getElementById('fileSize').textContent = '-';
            document.getElementById('extractedInfo').classList.add('d-none');
            document.getElementById('uploadProgress').classList.add('d-none');
            document.getElementById('uploadStatus').classList.add('d-none');
            fileUploaded = false;
            metadataRetrieved = false;
            tempFileId = null;
        }

        // Update sprint
        document.getElementById('selectedSprint').textContent = sprint.value ? sprint.text : 'Not selected';

        // Update changelog summary
        if (changelog) {
            const summary = changelog.length > 50 ? changelog.substring(0, 50) + '...' : changelog;
            document.getElementById('changelogSummary').textContent = summary;
        } else {
            document.getElementById('changelogSummary').textContent = 'Not provided';
        }

        // Update ticket links summary
        updateTicketLinksSummary();

        // Update submit button state
        updateSubmitButtonState();
    }

    // Chunked file upload to bypass POST size limits
    function uploadFileToTemp(file) {
        // Check file size before attempting upload (200MB limit)
        const maxUploadSize = 200 * 1024 * 1024; // 200MB

        if (file.size > maxUploadSize) {
            showAlert('error', `File size (${formatFileSize(file.size)}) exceeds 200MB limit. Please select a smaller file.`);
            return;
        }

        // Show progress bar
        document.getElementById('uploadProgress').classList.remove('d-none');
        document.getElementById('uploadStatus').classList.add('d-none');
        document.getElementById('extractedInfo').classList.add('d-none');

        // Generate UUID for this upload
        const uploadId = generateUUID();
        const chunkSize = 1024 * 1024; // 1MB chunks
        const totalChunks = Math.ceil(file.size / chunkSize);
        let currentChunk = 0;

        console.log(`Starting chunked upload: ${file.name}, Size: ${formatFileSize(file.size)}, Chunks: ${totalChunks}`);

        function uploadChunk() {
            const start = currentChunk * chunkSize;
            const end = Math.min(start + chunkSize, file.size);
            const chunk = file.slice(start, end);

            const formData = new FormData();
            formData.append('chunk', chunk);
            formData.append('upload_id', uploadId);
            formData.append('chunk_index', currentChunk);
            formData.append('total_chunks', totalChunks);
            formData.append('original_filename', file.name);
            formData.append('total_size', file.size);

            const xhr = new XMLHttpRequest();

            xhr.addEventListener('load', function() {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            currentChunk++;

                            // Update progress
                            const progress = (currentChunk / totalChunks) * 100;
                            const progressBar = document.getElementById('uploadProgressBar');
                            const percentage = document.getElementById('uploadPercentage');

                            progressBar.style.width = progress + '%';
                            percentage.textContent = Math.round(progress) + '%';

                            if (currentChunk < totalChunks) {
                                // Upload next chunk
                                setTimeout(uploadChunk, 10); // Small delay between chunks
                            } else {
                                // All chunks uploaded, finalize
                                finalizeUpload(uploadId, file.name);
                            }
                        } else {
                            showAlert('error', response.error || 'Chunk upload failed.');
                            document.getElementById('uploadProgress').classList.add('d-none');
                        }
                    } catch (e) {
                        showAlert('error', 'Invalid response from server during chunk upload.');
                        document.getElementById('uploadProgress').classList.add('d-none');
                    }
                } else {
                    showAlert('error', `Chunk upload failed with status: ${xhr.status}`);
                    document.getElementById('uploadProgress').classList.add('d-none');
                }
            });

            xhr.addEventListener('error', function() {
                showAlert('error', 'Network error during chunk upload.');
                document.getElementById('uploadProgress').classList.add('d-none');
            });

            xhr.open('POST', '/apps/upload-chunk');
            xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
            xhr.send(formData);
        }

        // Start uploading chunks
        uploadChunk();
    }

    // Finalize chunked upload
    function finalizeUpload(uploadId, originalFilename) {
        fetch('/apps/finalize-upload', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                upload_id: uploadId,
                original_filename: originalFilename
            })
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('uploadProgress').classList.add('d-none');

            if (data.success) {
                // File uploaded successfully
                tempFileId = data.file_uuid;
                fileUploaded = true;

                // Show success status with retrieve button
                document.getElementById('uploadStatus').classList.remove('d-none');

                // Enable retrieve metadata button
                if (retrieveBtn) {
                    retrieveBtn.disabled = false;
                    retrieveBtn.innerHTML = '<i class="fas fa-download me-1"></i>Retrieve Metadata';
                    console.log('✅ Retrieve Metadata button enabled after successful upload');
                } else {
                    console.error('❌ Retrieve button not found!');
                }

                console.log('✅ File uploaded successfully:', data);
                showAlert('success', 'File uploaded successfully! Click "Retrieve Metadata" to extract app information.');
                updateSubmitButtonState();
            } else {
                showAlert('error', data.error || 'Failed to finalize file upload.');
            }
        })
        .catch(error => {
            document.getElementById('uploadProgress').classList.add('d-none');
            console.error('Finalize upload error:', error);
            showAlert('error', 'Failed to finalize file upload. Please try again.');
        });
    }

    // Generate UUID for upload identification
    function generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    // Reset upload state for new file
    function resetUploadState() {
        // Reset state variables
        fileUploaded = false;
        metadataRetrieved = false;
        tempFileId = null;

        // Hide all status elements
        const statusElements = [
            'uploadProgress',
            'uploadStatus',
            'extractedInfo'
        ];

        statusElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                element.classList.add('d-none');
            }
        });

        // Reset progress bar
        const progressBar = document.getElementById('uploadProgressBar');
        const percentage = document.getElementById('uploadPercentage');
        if (progressBar && percentage) {
            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', '0');
            percentage.textContent = '0%';
        }

        // Reset retrieve metadata button
        if (retrieveBtn) {
            retrieveBtn.disabled = true;
            retrieveBtn.innerHTML = '<i class="fas fa-download me-1"></i>Retrieve Metadata';
        }

        // Reset extracted info to defaults
        const elements = [
            'extractedAppName',
            'extractedVersion',
            'extractedBuildNumber',
            'extractedBundleId',
            'extractedPlatform'
        ];

        elements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = 'Not extracted';
            }
        });

        // Reset app icon to placeholder
        const appIcon = document.getElementById('appIcon');
        const appIconPlaceholder = document.getElementById('appIconPlaceholder');

        if (appIcon && appIconPlaceholder) {
            appIcon.src = '';
            appIcon.alt = 'App Icon';
            appIcon.classList.add('d-none');
            appIconPlaceholder.classList.remove('d-none');
        }

        // Reset retrieve button
        if (retrieveBtn) {
            retrieveBtn.disabled = true;
            retrieveBtn.innerHTML = '<i class="fas fa-download me-1"></i>Retrieve Metadata';
        }

        // Update submit button state
        updateSubmitButtonState();

        console.log('✅ Upload state reset for new file');
    }



    // Retrieve metadata from uploaded file
    function retrieveMetadata() {
        if (!tempFileId) {
            showAlert('danger', 'No file uploaded. Please upload a file first.');
            return;
        }

        console.log('🔍 Starting metadata extraction for file:', tempFileId);

        // Show loading state
        document.getElementById('extractedInfo').classList.remove('d-none');
        document.getElementById('extractedAppName').textContent = 'Unzipping file...';
        document.getElementById('extractedVersion').textContent = 'Reading .plist/.xml...';
        document.getElementById('extractedBuildNumber').textContent = 'Parsing metadata...';
        document.getElementById('extractedBundleId').textContent = 'Extracting info...';
        document.getElementById('extractedPlatform').textContent = 'Processing...';

        // Disable retrieve button
        retrieveBtn.disabled = true;
        retrieveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Unzipping & Extracting...';

        // Show progress message
        showAlert('info', 'Unzipping file and extracting metadata from .plist/.xml files...', false);

        // Call Laravel API to extract metadata using file UUID
        fetch('/apps/extract-metadata', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                file_uuid: tempFileId
            })
        })
        .then(response => response.json())
        .then(data => {
            // Clear progress alert
            const progressAlert = document.querySelector('.alert-info');
            if (progressAlert) {
                progressAlert.remove();
            }

            if (data.success) {
                // Debug: Log the complete response
                console.log('🔍 Complete metadata response:', JSON.stringify(data, null, 2));
                console.log('🔍 Data object:', data.data);
                console.log('🔍 Plist data:', data.data.plist_data);

                // Validate and store extracted metadata globally for form submission
                if (data.data && typeof data.data === 'object') {
                    window.extractedMetadata = data.data;
                    console.log('✅ Metadata stored for form submission');
                } else {
                    console.error('❌ Invalid metadata structure received');
                    showAlert('error', 'Invalid metadata structure received from server');
                    return;
                }

                // Handle app icon if available
                if (data.data.icon_path) {
                    const appIcon = document.getElementById('appIcon');
                    const appIconPlaceholder = document.getElementById('appIconPlaceholder');

                    if (appIcon && appIconPlaceholder) {
                        appIcon.src = '/storage/' + data.data.icon_path;
                        appIcon.alt = data.data.app_name + ' Icon';
                        appIcon.classList.remove('d-none');
                        appIconPlaceholder.classList.add('d-none');
                    }
                }

                // Set metadata retrieved flag
                metadataRetrieved = true;

                console.log('✅ Metadata extraction successful:', data.data);
                console.log('📱 Plist data keys:', Object.keys(data.data.plist_data || {}));
                console.log('📋 Full plist data:', data.data.plist_data);

                // Display the plist data in App Information section
                // Handle nested plist_data structure
                let plistData = data.data.plist_data || {};

                // Check if plist_data contains another plist_data (nested structure)
                if (plistData.plist_data) {
                    console.log('🔍 Found nested plist_data structure');
                    plistData = plistData.plist_data;
                }

                console.log('🔍 About to display plist data:', plistData);
                console.log('🔍 Plist data type:', typeof plistData);
                console.log('🔍 Plist data keys:', Object.keys(plistData));

                if (Object.keys(plistData).length === 0) {
                    console.warn('⚠️ No plist data found in response!');
                    console.log('🔍 Full response data:', data.data);
                }

                displayPlistInformation(plistData);

                // Display app icon if available
                displayAppIcon(data.data);

                // Show success message with extraction details
                const extractionMethod = data.data.extraction_method || 'file_analysis';
                const sourceFile = data.data.platform === 'ios' ? 'Info.plist' : 'AndroidManifest.xml';
                showAlert('success', `Successfully unzipped and extracted metadata from ${sourceFile} file!`);

                // Hide retrieve button
                document.getElementById('uploadStatus').classList.add('d-none');

                updateSubmitButtonState();
            } else {
                // Show error
                console.error('Metadata extraction failed:', data);

                // Reset to default values
                document.getElementById('extractedAppName').textContent = 'Extraction Failed';
                document.getElementById('extractedVersion').textContent = 'Unknown';
                document.getElementById('extractedBuildNumber').textContent = 'Unknown';
                document.getElementById('extractedBundleId').textContent = 'Unknown';
                document.getElementById('extractedPlatform').textContent = 'Unknown';

                let errorMessage = data.error || 'Failed to unzip or extract metadata from file.';
                showAlert('danger', 'Extraction Error: ' + errorMessage);

                // Reset retrieve button
                retrieveBtn.disabled = false;
                retrieveBtn.innerHTML = '<i class="fas fa-download me-1"></i>Retrieve Metadata';
            }
        })
        .catch(error => {
            console.error('Metadata extraction network error:', error);

            // Clear progress alert
            const progressAlert = document.querySelector('.alert-info');
            if (progressAlert) {
                progressAlert.remove();
            }

            // Reset extracted info to error state
            document.getElementById('extractedAppName').textContent = 'Network Error';
            document.getElementById('extractedVersion').textContent = 'Failed';
            document.getElementById('extractedBuildNumber').textContent = 'Failed';
            document.getElementById('extractedBundleId').textContent = 'Failed';
            document.getElementById('extractedPlatform').textContent = 'Failed';

            showAlert('danger', 'Network error during file unzipping and metadata extraction. Please try again.');

            // Reset retrieve button
            retrieveBtn.disabled = false;
            retrieveBtn.innerHTML = '<i class="fas fa-download me-1"></i>Retrieve Metadata';
        });
    }

    // Update submit button state based on form completion
    function updateSubmitButtonState() {
        const sprint = sprintSelect.value;
        const changelog = changelogTextarea.value.trim();

        const isFormComplete = fileUploaded && metadataRetrieved && sprint && changelog && tempFileId;

        submitBtn.disabled = !isFormComplete;

        if (isFormComplete) {
            document.getElementById('submitBtnHelp').textContent = 'Ready to upload app';
            document.getElementById('submitBtnHelp').className = 'text-success ms-2';
        } else {
            let missing = [];
            if (!fileUploaded) missing.push('file upload');
            if (!metadataRetrieved) missing.push('metadata retrieval');
            if (!sprint) missing.push('sprint selection');
            if (!changelog) missing.push('changelog');
            if (!tempFileId) missing.push('temp file ID');

            document.getElementById('submitBtnHelp').textContent = `Missing: ${missing.join(', ')}`;
            document.getElementById('submitBtnHelp').className = 'text-muted ms-2';
        }
    }

    // Function to display plist information in App Information section
    function displayPlistInformation(plistData) {
        console.log('🔍 Displaying plist information:', plistData);
        console.log('🔍 Plist data type in function:', typeof plistData);
        console.log('🔍 Is plist data an object?', typeof plistData === 'object');
        console.log('🔍 Plist data keys in function:', Object.keys(plistData || {}));

        // Check if plist data is empty
        if (!plistData || Object.keys(plistData).length === 0) {
            console.warn('⚠️ No plist data to display!');
            // Set all fields to indicate no data
            const fields = ['extractedAppName', 'extractedVersion', 'extractedBuildNumber', 'extractedBundleId', 'extractedPlatform', 'extractedMinOS', 'extractedExecutable', 'extractedUrlSchemes', 'extractedPermissions', 'extractedSupportedPlatforms', 'extractedDeviceFamily'];
            fields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.textContent = 'No data extracted';
                }
            });
            return;
        }

        // Helper function to safely set element content
        function setElementContent(id, content) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = content || '-';
                console.log(`✅ Set ${id} to: ${content || '-'}`);
            } else {
                console.warn(`⚠️ Element not found: ${id}`);
            }
        }

        // Helper function to get value with fallbacks
        function getValueWithFallback(data, keys, defaultValue = '-') {
            for (const key of keys) {
                if (data[key] && data[key] !== '') {
                    return data[key];
                }
            }
            return defaultValue;
        }

        // Extract and display basic app information
        const appName = getValueWithFallback(plistData, ['CFBundleDisplayName', 'CFBundleName'], 'Unknown App');
        const version = getValueWithFallback(plistData, ['CFBundleShortVersionString'], '1.0.0');
        const buildNumber = getValueWithFallback(plistData, ['CFBundleVersion'], '1');
        const bundleId = getValueWithFallback(plistData, ['CFBundleIdentifier'], 'com.unknown.app');
        const minOsVersion = getValueWithFallback(plistData, ['MinimumOSVersion'], 'Unknown');
        const executable = getValueWithFallback(plistData, ['CFBundleExecutable'], 'Unknown');

        // Update basic information
        setElementContent('extractedAppName', appName);
        setElementContent('extractedVersion', version);
        setElementContent('extractedBuildNumber', buildNumber);
        setElementContent('extractedBundleId', bundleId);
        setElementContent('extractedPlatform', 'iOS');
        setElementContent('extractedMinOS', minOsVersion);
        setElementContent('extractedExecutable', executable);

        // Extract and display URL schemes
        const urlSchemes = [];
        const urlTypes = plistData['CFBundleURLTypes'] || [];
        urlTypes.forEach(urlType => {
            if (urlType['CFBundleURLSchemes']) {
                urlSchemes.push(...urlType['CFBundleURLSchemes']);
            }
        });
        setElementContent('extractedUrlSchemes', urlSchemes.length > 0 ? urlSchemes.join(', ') : 'None');

        // Extract and display permissions
        const permissions = [];
        Object.keys(plistData).forEach(key => {
            if (key.includes('UsageDescription')) {
                permissions.push(key.replace('NS', '').replace('UsageDescription', ''));
            }
        });
        setElementContent('extractedPermissions', permissions.length > 0 ? `${permissions.length} permissions: ${permissions.join(', ')}` : 'None');

        // Extract and display supported platforms
        const supportedPlatforms = plistData['CFBundleSupportedPlatforms'] || [];
        setElementContent('extractedSupportedPlatforms', supportedPlatforms.length > 0 ? supportedPlatforms.join(', ') : 'iOS');

        // Extract and display device family
        const deviceFamily = plistData['UIDeviceFamily'] || [];
        if (deviceFamily.length > 0) {
            const deviceNames = deviceFamily.map(family => {
                switch(parseInt(family)) {
                    case 1: return 'iPhone';
                    case 2: return 'iPad';
                    case 3: return 'Apple TV';
                    case 4: return 'Apple Watch';
                    default: return `Device ${family}`;
                }
            });
            setElementContent('extractedDeviceFamily', deviceNames.join(', '));
        } else {
            setElementContent('extractedDeviceFamily', 'Universal');
        }

        // Display raw plist data
        const rawPlistElement = document.getElementById('rawPlistContent');
        if (rawPlistElement) {
            rawPlistElement.textContent = JSON.stringify(plistData, null, 2);
        }

        // Log extracted information for debugging
        console.log('📱 Extracted App Information:', {
            appName, version, buildNumber, bundleId, minOsVersion, executable,
            urlSchemes, permissions, supportedPlatforms, deviceFamily
        });
        console.log('📋 Total plist keys found:', Object.keys(plistData).length);
    }

    // Function to display app icon
    function displayAppIcon(iconData) {
        const appIconImg = document.getElementById('appIcon');
        const appIconPlaceholder = document.getElementById('appIconPlaceholder');

        if (iconData && iconData.icon_filename) {
            // Construct icon URL - assuming it's in temp_icons directory
            const iconUrl = `/storage/temp_icons/${iconData.icon_filename}`;

            if (appIconImg) {
                appIconImg.src = iconUrl;
                appIconImg.classList.remove('d-none');
                appIconImg.onerror = function() {
                    // If icon fails to load, hide it and show placeholder
                    this.classList.add('d-none');
                    if (appIconPlaceholder) {
                        appIconPlaceholder.classList.remove('d-none');
                    }
                };
            }

            if (appIconPlaceholder) {
                appIconPlaceholder.classList.add('d-none');
            }

            console.log('📱 App icon displayed:', iconUrl);
        } else {
            // No icon available, show placeholder
            if (appIconImg) {
                appIconImg.classList.add('d-none');
            }
            if (appIconPlaceholder) {
                appIconPlaceholder.classList.remove('d-none');
            }
            console.log('📱 No app icon available, showing placeholder');
        }
    }

    // File input change handler
    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            // Reset all states for new upload
            resetUploadState();
            // Start upload process
            uploadFileToTemp(file);
        }
        // Don't call updateUploadSummary and validateFile here as they trigger another upload
        // updateUploadSummary();
        // validateFile();
    });

    // Sprint change handler
    sprintSelect.addEventListener('change', function() {
        updateUploadSummary();
        updateSubmitButtonState();
    });

    // Changelog change handler - Only update button state, don't trigger file upload
    changelogTextarea.addEventListener('input', function() {
        updateSubmitButtonState();
    });

    // Retrieve metadata button handler
    retrieveBtn.addEventListener('click', retrieveMetadata);

    // Update ticket links summary
    function updateTicketLinksSummary() {
        const ticketInputs = document.querySelectorAll('input[name="ticket_links[]"]');
        const links = Array.from(ticketInputs)
            .map(input => input.value.trim())
            .filter(value => value !== '');

        if (links.length > 0) {
            document.getElementById('ticketLinksSummary').textContent = `${links.length} link(s) added`;
        } else {
            document.getElementById('ticketLinksSummary').textContent = 'None added';
        }
    }

    // Add ticket link input
    window.addTicketLink = function() {
        const container = document.getElementById('ticketLinksContainer');
        const index = container.children.length;

        const newRow = document.createElement('div');
        newRow.className = 'ticket-link-row mb-2';
        newRow.innerHTML = `
            <div class="input-group">
                <input type="url"
                       class="form-control"
                       name="ticket_links[]"
                       placeholder="https://jira.company.com/ticket/123"
                       onchange="updateTicketLinksSummary()">
                <button type="button" class="btn btn-outline-danger" onclick="removeTicketLink(this)">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        `;

        container.appendChild(newRow);
        updateTicketLinksSummary();
    };

    // Update ticket links summary
    window.updateTicketLinksSummary = function() {
        // This function can be implemented if needed for ticket links summary
        console.log('Ticket links updated');
    };

    // Remove ticket link input
    window.removeTicketLink = function(button) {
        const container = document.getElementById('ticketLinksContainer');
        if (container.children.length > 1) {
            button.closest('.ticket-link-row').remove();
            updateTicketLinksSummary();
        }
    };

    // Add event listeners to existing ticket link inputs
    document.addEventListener('change', function(e) {
        if (e.target.name === 'ticket_links[]') {
            updateTicketLinksSummary();
        }
    });

    // Validate file
    function validateFile() {
        const file = fileInput.files[0];

        if (!file) return;

        const fileName = file.name.toLowerCase();
        const isValid = fileName.endsWith('.ipa') || fileName.endsWith('.apk');

        if (!isValid) {
            fileInput.classList.add('is-invalid');
            showAlert('warning', 'Please select a valid app file (IPA or APK).');
        } else {
            fileInput.classList.remove('is-invalid');
        }
    }

    // Form submission with progress tracking
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Create FormData manually to exclude file input
        const formData = new FormData();

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
            formData.append('_token', csrfToken);
        }

        // Add form fields except file input
        const formElements = form.elements;
        for (let i = 0; i < formElements.length; i++) {
            const element = formElements[i];
            if (element.name && element.name !== 'file' && element.type !== 'file') {
                if (element.type === 'checkbox' || element.type === 'radio') {
                    if (element.checked) {
                        formData.append(element.name, element.value);
                    }
                } else if (element.type !== 'submit' && element.type !== 'button') {
                    formData.append(element.name, element.value);
                }
            }
        }

        // Add extracted metadata if available
        if (window.extractedMetadata) {
            const metadataJson = JSON.stringify(window.extractedMetadata);
            formData.append('extracted_metadata', metadataJson);
            console.log('📤 Submitting metadata:', metadataJson.substring(0, 200) + '...');
        } else {
            console.warn('⚠️ No extracted metadata available for submission');
            showAlert('error', 'No metadata available. Please retrieve metadata first.');
            return;
        }

        // Add temp file ID if available
        if (tempFileId) {
            formData.append('temp_file_id', tempFileId);
            console.log('📤 Submitting temp_file_id:', tempFileId);
        } else {
            console.warn('⚠️ No temp file ID available for submission');
            showAlert('error', 'No file uploaded. Please upload a file first.');
            return;
        }

        const progressModal = new bootstrap.Modal(document.getElementById('uploadProgressModal'));

        // Show progress modal
        progressModal.show();

        // Disable submit button
        submitBtn.disabled = true;
        submitBtn.querySelector('.btn-text').textContent = 'Uploading...';
        submitBtn.querySelector('.spinner-border').classList.remove('d-none');

        // Create XMLHttpRequest for progress tracking
        const xhr = new XMLHttpRequest();

        xhr.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                const progressBar = document.getElementById('uploadProgress');
                progressBar.style.width = percentComplete + '%';
                progressBar.textContent = Math.round(percentComplete) + '%';

                document.getElementById('uploadStatus').textContent =
                    `Uploading... ${Math.round(percentComplete)}%`;
            }
        });

        xhr.addEventListener('load', function() {
            progressModal.hide();

            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        showAlert('success', response.message || 'App uploaded successfully!');
                        setTimeout(() => {
                            window.location.href = response.redirect_url || '{{ route("apps.index") }}';
                        }, 2000);
                    } else {
                        showAlert('danger', response.message || 'Upload failed. Please try again.');
                        console.error('Upload failed:', response);
                        resetSubmitButton();
                    }
                } catch (e) {
                    console.error('JSON parse error:', e);
                    console.error('Response text:', xhr.responseText);
                    showAlert('danger', 'Invalid response from server');
                    resetSubmitButton();
                }
            } else {
                console.error('HTTP error:', xhr.status, xhr.statusText);
                console.error('Response text:', xhr.responseText);
                showAlert('danger', `Upload failed with status: ${xhr.status} - ${xhr.statusText}`);
                resetSubmitButton();
            }
        });

        xhr.addEventListener('error', function() {
            progressModal.hide();
            console.error('Network error during upload');
            showAlert('danger', 'Upload failed. Please check your connection and try again.');
            resetSubmitButton();
        });

        xhr.addEventListener('timeout', function() {
            progressModal.hide();
            console.error('Upload timeout');
            showAlert('danger', 'Upload timed out. Please try again.');
            resetSubmitButton();
        });

        xhr.timeout = 300000; // 5 minutes timeout
        xhr.open('POST', form.action);
        xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
        xhr.setRequestHeader('Accept', 'application/json');
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        xhr.send(formData);
    });

    function resetSubmitButton() {
        submitBtn.disabled = false;
        submitBtn.querySelector('.btn-text').textContent = 'Upload App';
        submitBtn.querySelector('.spinner-border').classList.add('d-none');
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function showAlert(type, message, autoClose = true) {
        const alertId = 'alert-' + Date.now();
        const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        document.querySelector('.card-body').insertAdjacentHTML('afterbegin', alertHtml);

        // Auto-close after 4 seconds
        if (autoClose) {
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    const bsAlert = new bootstrap.Alert(alertElement);
                    bsAlert.close();
                }
            }, 4000);
        }
    }

    // Initialize summary
    updateUploadSummary();
});
</script>
@endpush
