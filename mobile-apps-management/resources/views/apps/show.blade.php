@extends('layouts.app')

@section('title', 'App Details')
@section('page-title', $app->name . ' v' . $app->version)

@section('content')
<div class="row">
    <!-- App Information -->
    <div class="col-md-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-mobile-alt me-2 text-primary"></i>
                        App Details
                    </h5>
                    <div class="btn-group">
                        @can('apps.update')
                        <a href="{{ route('apps.edit', $app) }}" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>Edit App
                        </a>
                        @endcan
                        <a href="{{ route('apps.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Apps
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- App Header -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <div class="app-icon me-3">
                                @if($app->icon_path)
                                    <img src="{{ $app->getAppIconUrl() }}" alt="{{ $app->name }}" class="rounded" style="width: 80px; height: 80px;">
                                @else
                                    <div class="bg-{{ $app->getPlatformColor() }} text-white rounded d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                        <i class="{{ $app->getPlatformIcon() }} fa-2x"></i>
                                    </div>
                                @endif
                            </div>
                            <div>
                                <h3 class="mb-1">{{ $app->name }}</h3>
                                <p class="text-muted mb-1">Version {{ $app->version }}</p>
                                @if($app->build_number)
                                <p class="text-muted mb-1">Build {{ $app->build_number }}</p>
                                @endif
                                <div class="mt-2">
                                    <span class="badge {{ $app->getStatusBadgeClass() }} me-2">
                                        {{ $app->getStatusText() }}
                                    </span>
                                    <span class="badge bg-{{ $app->getPlatformColor() }}">
                                        <i class="{{ $app->getPlatformIcon() }} me-1"></i>
                                        {{ ucfirst($app->platform) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="border-end">
                                    <h5 class="text-primary mb-0">{{ number_format($downloadStats['total_downloads']) }}</h5>
                                    <small class="text-muted">Downloads</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h5 class="text-success mb-0">{{ number_format($downloadStats['successful_downloads']) }}</h5>
                                    <small class="text-muted">Successful</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h5 class="text-info mb-0">{{ number_format($downloadStats['unique_downloaders']) }}</h5>
                                <small class="text-muted">Unique Users</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- App Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-muted">Bundle Identifier</h6>
                        <p class="font-monospace">{{ $app->bundle_identifier }}</p>
                        
                        <h6 class="text-muted">Sprint</h6>
                        <p>
                            @if($app->sprint)
                                <span class="badge bg-info">{{ $app->sprint->name }}</span>
                            @else
                                <span class="text-muted">Not assigned</span>
                            @endif
                        </p>
                        
                        <h6 class="text-muted">File Size</h6>
                        <p>{{ $app->getFileSizeFormatted() }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Uploaded By</h6>
                        <p>{{ $app->uploader->name ?? 'Unknown' }}</p>
                        
                        <h6 class="text-muted">Upload Date</h6>
                        <p>{{ $app->created_at->format('F d, Y \a\t g:i A') }}</p>
                        
                        @if($app->ticket_link)
                        <h6 class="text-muted">Related Ticket</h6>
                        <p>
                            <a href="{{ $app->ticket_link }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-external-link-alt me-1"></i>View Ticket
                            </a>
                        </p>
                        @endif
                    </div>
                </div>

                <!-- Changelog -->
                @if($app->changelog)
                <div class="mb-4">
                    <h6 class="text-muted">Changelog</h6>
                    <div class="bg-light p-3 rounded">
                        <pre class="mb-0">{{ $app->changelog }}</pre>
                    </div>
                </div>
                @endif

                <!-- File Information -->
                <div class="mb-4">
                    <h6 class="text-muted">File Information</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>File Name:</strong></td>
                                <td>{{ $app->file_name ?? 'N/A' }}</td>
                            </tr>
                            <tr>
                                <td><strong>MIME Type:</strong></td>
                                <td>{{ $app->mime_type ?? 'N/A' }}</td>
                            </tr>
                            <tr>
                                <td><strong>File Hash:</strong></td>
                                <td class="font-monospace small">{{ $app->file_hash }}</td>
                            </tr>
                            @if($app->metadata)
                            <tr>
                                <td><strong>Metadata:</strong></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-info" data-bs-toggle="collapse" data-bs-target="#metadataCollapse">
                                        <i class="fas fa-info-circle me-1"></i>View Details
                                    </button>
                                </td>
                            </tr>
                            @endif
                        </table>
                    </div>
                    
                    @if($app->metadata)
                    <div class="collapse mt-3" id="metadataCollapse">
                        <div class="bg-light p-3 rounded">
                            <pre class="mb-0">{{ json_encode($app->metadata, JSON_PRETTY_PRINT) }}</pre>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Recent Downloads -->
                @if($downloadStats['recent_downloads']->count() > 0)
                <div class="mb-4">
                    <h6 class="text-muted">Recent Downloads</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($downloadStats['recent_downloads'] as $download)
                                <tr>
                                    <td>{{ $download->user->name ?? 'Unknown' }}</td>
                                    <td>{{ $download->created_at->diffForHumans() }}</td>
                                    <td>
                                        <span class="badge bg-{{ $download->success ? 'success' : 'danger' }}">
                                            {{ $download->success ? 'Success' : 'Failed' }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Actions & QR Code -->
    <div class="col-md-4">
        <!-- Quick Actions -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-0">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    @if($app->isQuarantined() && $app->canBeReleased())
                        @can('apps.update')
                        <button type="button" 
                                class="btn btn-success"
                                onclick="releaseFromQuarantine({{ $app->id }}, '{{ $app->name }}')">
                            <i class="fas fa-check me-2"></i>Release from Quarantine
                        </button>
                        @endcan
                    @elseif($app->isActive())
                        @can('apps.download')
                        <button type="button" 
                                class="btn btn-primary"
                                onclick="generateDownloadToken({{ $app->id }})">
                            <i class="fas fa-download me-2"></i>Download App
                        </button>
                        @endcan
                    @endif

                    @can('apps.update')
                    <button type="button" 
                            class="btn btn-outline-warning"
                            onclick="regenerateToken({{ $app->id }})">
                        <i class="fas fa-sync me-2"></i>Regenerate Token
                    </button>
                    @endcan

                    @can('apps.read')
                    <button type="button" 
                            class="btn btn-outline-info"
                            onclick="viewDownloadStats({{ $app->id }})">
                        <i class="fas fa-chart-line me-2"></i>Download Stats
                    </button>
                    @endcan

                    @can('apps.delete')
                    <button type="button" 
                            class="btn btn-outline-danger"
                            onclick="deleteApp({{ $app->id }}, '{{ $app->name }}')">
                        <i class="fas fa-trash me-2"></i>Delete App
                    </button>
                    @endcan
                </div>
            </div>
        </div>

        <!-- QR Code -->
        @if($app->qr_code_path && $app->isActive())
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-0">
                <h6 class="mb-0">
                    <i class="fas fa-qrcode me-2"></i>QR Code
                </h6>
            </div>
            <div class="card-body text-center">
                <img src="{{ asset('storage/' . $app->qr_code_path) }}" 
                     alt="QR Code for {{ $app->name }}" 
                     class="img-fluid"
                     style="max-width: 200px;">
                <p class="small text-muted mt-2">
                    Scan to download on mobile device
                </p>
            </div>
        </div>
        @endif

        <!-- Status Information -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Status Information
                </h6>
            </div>
            <div class="card-body">
                @if($app->isQuarantined())
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-shield-alt me-2"></i>In Quarantine</h6>
                        @if($app->quarantine_until)
                            <p class="mb-1">Until: {{ $app->quarantine_until->format('M d, Y H:i') }}</p>
                            <p class="mb-0">{{ $app->quarantine_until->diffForHumans() }}</p>
                        @else
                            <p class="mb-0">Manual review required</p>
                        @endif
                    </div>
                @elseif($app->isActive())
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>Active</h6>
                        <p class="mb-0">App is available for download</p>
                        @if($app->released_at)
                            <small class="text-muted">Released: {{ $app->released_at->diffForHumans() }}</small>
                        @endif
                    </div>
                @else
                    <div class="alert alert-secondary">
                        <h6><i class="fas fa-pause-circle me-2"></i>Inactive</h6>
                        <p class="mb-0">App is not available for download</p>
                    </div>
                @endif

                <div class="mt-3">
                    <small class="text-muted d-block">Access Token:</small>
                    <code class="small">{{ Str::limit($app->access_token, 20) }}...</code>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Release from quarantine
function releaseFromQuarantine(appId, appName) {
    if (confirm(`Release "${appName}" from quarantine?`)) {
        fetch(`/apps/${appId}/release-quarantine`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => window.location.reload(), 1500);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Failed to release app from quarantine');
        });
    }
}

// Generate download token
function generateDownloadToken(appId) {
    fetch(`/files/${appId}/generate-token`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.open(data.download_url, '_blank');
            showAlert('success', 'Download started');
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Failed to generate download token');
    });
}

// Regenerate access token
function regenerateToken(appId) {
    if (confirm('Regenerate access token? This will invalidate the current token.')) {
        fetch(`/apps/${appId}/regenerate-token`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => window.location.reload(), 1500);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Failed to regenerate token');
        });
    }
}

// View download stats
function viewDownloadStats(appId) {
    window.open(`/apps/${appId}/download-stats`, '_blank');
}

// Delete app
function deleteApp(appId, appName) {
    if (confirm(`Are you sure you want to delete "${appName}"? This action cannot be undone.`)) {
        fetch(`/apps/${appId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => window.location.href = '{{ route("apps.index") }}', 1500);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'An error occurred while deleting the app.');
        });
    }
}

// Show alert function
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.querySelector('.card-body').insertAdjacentHTML('afterbegin', alertHtml);
}
</script>
@endpush
