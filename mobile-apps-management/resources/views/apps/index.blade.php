@extends('layouts.app')

@section('title', 'Apps Management')
@section('page-title', 'Apps Management')

@section('content')
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="fas fa-mobile-alt fa-2x text-primary me-3"></i>
                    <div>
                        <h3 class="mb-0">{{ $stats['total_apps'] }}</h3>
                        <small class="text-muted">Total Apps</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="fas fa-download fa-2x text-success me-3"></i>
                    <div>
                        <h3 class="mb-0">{{ number_format($stats['total_downloads']) }}</h3>
                        <small class="text-muted">Downloads</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="fas fa-hdd fa-2x text-info me-3"></i>
                    <div>
                        <h3 class="mb-0">{{ \App\Models\App::find(1)?->getFileSizeFormatted() ?? '0 B' }}</h3>
                        <small class="text-muted">Storage Used</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="fas fa-shield-alt fa-2x text-warning me-3"></i>
                    <div>
                        <h3 class="mb-0">{{ $stats['quarantined_count'] }}</h3>
                        <small class="text-muted">Quarantined</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-mobile-alt me-2 text-primary"></i>
                        Apps Management
                    </h5>
                    <div class="btn-group">
                        @can('apps.upload')
                        <a href="{{ route('files.storage-management') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-hdd me-2"></i>File Storage
                        </a>
                        @endcan
                        @can('apps.create')
                        <a href="{{ route('apps.create') }}" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>Upload App
                        </a>
                        @endcan
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Search and Filter -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <form method="GET" action="{{ route('apps.index') }}" class="d-flex">
                            <input type="text"
                                   name="search"
                                   class="form-control me-2"
                                   placeholder="Search apps..."
                                   value="{{ request('search') }}">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i>
                            </button>
                            @if(request('search'))
                            <a href="{{ route('apps.index') }}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-times"></i>
                            </a>
                            @endif
                        </form>
                    </div>
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-3">
                                <select name="platform" class="form-select" onchange="filterApps()">
                                    <option value="">All Platforms</option>
                                    @foreach($platforms as $platform)
                                    <option value="{{ $platform }}" {{ request('platform') === $platform ? 'selected' : '' }}>
                                        {{ ucfirst($platform) }}
                                    </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="sprint" class="form-select" onchange="filterApps()">
                                    <option value="">All Sprints</option>
                                    @foreach($sprints as $sprint)
                                    <option value="{{ $sprint->id }}" {{ request('sprint') == $sprint->id ? 'selected' : '' }}>
                                        {{ $sprint->name }}
                                    </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="status" class="form-select" onchange="filterApps()">
                                    <option value="">All Status</option>
                                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="quarantined" {{ request('status') === 'quarantined' ? 'selected' : '' }}>Quarantined</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <div class="btn-group w-100">
                                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-sort me-2"></i>Sort
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['sort_by' => 'name', 'sort_direction' => 'asc']) }}">Name (A-Z)</a></li>
                                        <li><a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['sort_by' => 'created_at', 'sort_direction' => 'desc']) }}">Newest First</a></li>
                                        <li><a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['sort_by' => 'download_count', 'sort_direction' => 'desc']) }}">Most Downloaded</a></li>
                                        <li><a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['sort_by' => 'file_size', 'sort_direction' => 'desc']) }}">Largest Files</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                @if($apps->count() > 0)
                    <!-- Apps Grid -->
                    <div class="row">
                        @foreach($apps as $app)
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-start mb-3">
                                        <div class="app-icon me-3">
                                            @if($app->icon_path)
                                                <img src="{{ $app->getAppIconUrl() }}" alt="{{ $app->name }}" class="rounded" style="width: 50px; height: 50px;">
                                            @else
                                                <div class="bg-{{ $app->getPlatformColor() }} text-white rounded d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                    <i class="{{ $app->getPlatformIcon() }} fa-lg"></i>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">{{ $app->name }}</h6>
                                            <small class="text-muted">v{{ $app->version }}</small>
                                            @if($app->build_number)
                                                <small class="text-muted">({{ $app->build_number }})</small>
                                            @endif
                                            <div class="mt-1">
                                                <span class="badge {{ $app->getStatusBadgeClass() }}">
                                                    {{ $app->getStatusText() }}
                                                </span>
                                                <span class="badge bg-{{ $app->getPlatformColor() }}">
                                                    <i class="{{ $app->getPlatformIcon() }} me-1"></i>
                                                    {{ ucfirst($app->platform) }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <small class="text-muted d-block">Sprint: {{ $app->sprint->name ?? 'N/A' }}</small>
                                        <small class="text-muted d-block">Size: {{ $app->getFileSizeFormatted() }}</small>
                                        <small class="text-muted d-block">Downloads: {{ number_format($app->download_count) }}</small>
                                        <small class="text-muted d-block">Uploaded: {{ $app->created_at->diffForHumans() }}</small>
                                    </div>

                                    @if($app->changelog)
                                    <div class="mb-3">
                                        <small class="text-muted">{{ Str::limit($app->changelog, 100) }}</small>
                                    </div>
                                    @endif

                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="btn-group btn-group-sm">
                                            @can('apps.read')
                                            <a href="{{ route('apps.show', $app) }}" class="btn btn-outline-info" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @endcan

                                            @can('apps.update')
                                            <a href="{{ route('apps.edit', $app) }}" class="btn btn-outline-primary" title="Edit App">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @endcan

                                            @can('apps.delete')
                                            <button type="button" class="btn btn-outline-danger" title="Delete App" onclick="deleteApp({{ $app->id }}, '{{ $app->name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            @endcan
                                        </div>

                                        @if($app->isQuarantined() && $app->canBeReleased())
                                            @can('apps.update')
                                            <button type="button" class="btn btn-sm btn-success" onclick="releaseFromQuarantine({{ $app->id }}, '{{ $app->name }}')">
                                                <i class="fas fa-check me-1"></i>Release
                                            </button>
                                            @endcan
                                        @elseif($app->isActive())
                                            @can('apps.download')
                                            <button type="button" class="btn btn-sm btn-primary" onclick="generateDownloadToken({{ $app->id }})">
                                                <i class="fas fa-download me-1"></i>Download
                                            </button>
                                            @endcan
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <small class="text-muted">
                                Showing {{ $apps->firstItem() }} to {{ $apps->lastItem() }} of {{ $apps->total() }} apps
                            </small>
                        </div>
                        <div>
                            {{ $apps->links() }}
                        </div>
                    </div>
                @else
                    <!-- Empty State -->
                    <div class="text-center py-5">
                        <i class="fas fa-mobile-alt fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">No Apps Found</h4>
                        @if(request('search') || request('platform') || request('sprint') || request('status'))
                            <p class="text-muted">No apps match your search criteria.</p>
                            <a href="{{ route('apps.index') }}" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left me-2"></i>View All Apps
                            </a>
                        @else
                            <p class="text-muted">Get started by uploading your first app.</p>
                            @can('apps.create')
                            <a href="{{ route('apps.create') }}" class="btn btn-primary">
                                <i class="fas fa-upload me-2"></i>Upload First App
                            </a>
                            @endcan
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Filter functionality
function filterApps() {
    const platform = document.querySelector('select[name="platform"]').value;
    const sprint = document.querySelector('select[name="sprint"]').value;
    const status = document.querySelector('select[name="status"]').value;
    const search = document.querySelector('input[name="search"]').value;

    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (platform) params.append('platform', platform);
    if (sprint) params.append('sprint', sprint);
    if (status) params.append('status', status);

    window.location.href = '{{ route("apps.index") }}' + (params.toString() ? '?' + params.toString() : '');
}

// Release app from quarantine
function releaseFromQuarantine(appId, appName) {
    if (confirm(`Release "${appName}" from quarantine?`)) {
        fetch(`/files/${appId}/release-quarantine`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => window.location.reload(), 1500);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Failed to release app from quarantine');
        });
    }
}

// Generate download token
function generateDownloadToken(appId) {
    fetch(`/files/${appId}/generate-token`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Open download in new tab
            window.open(data.download_url, '_blank');
            showAlert('success', 'Download started');
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Failed to generate download token');
    });
}

// Delete app
function deleteApp(appId, appName) {
    if (confirm(`Are you sure you want to delete "${appName}"? This action cannot be undone.`)) {
        fetch(`/apps/${appId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => window.location.reload(), 1500);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'An error occurred while deleting the app.');
        });
    }
}

// Show alert function
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.querySelector('.row').insertAdjacentHTML('afterbegin', alertHtml);
}
</script>
@endpush
