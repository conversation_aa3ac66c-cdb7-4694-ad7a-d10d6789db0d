@extends('layouts.app')

@section('title', 'Edit App')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Edit App</h1>
                    <p class="text-muted">Update app information and settings</p>
                </div>
                <a href="{{ route('apps.show', $app) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to App
                </a>
            </div>

            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-edit text-primary me-2"></i>
                                App Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('apps.update', $app) }}" method="POST">
                                @csrf
                                @method('PUT')

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">App Name</label>
                                            <input type="text" 
                                                   class="form-control @error('name') is-invalid @enderror" 
                                                   id="name" 
                                                   name="name" 
                                                   value="{{ old('name', $app->name) }}" 
                                                   required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="version" class="form-label">Version</label>
                                            <input type="text" 
                                                   class="form-control @error('version') is-invalid @enderror" 
                                                   id="version" 
                                                   name="version" 
                                                   value="{{ old('version', $app->version) }}" 
                                                   required>
                                            @error('version')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="build_number" class="form-label">Build Number</label>
                                            <input type="text" 
                                                   class="form-control @error('build_number') is-invalid @enderror" 
                                                   id="build_number" 
                                                   name="build_number" 
                                                   value="{{ old('build_number', $app->build_number) }}">
                                            @error('build_number')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="bundle_identifier" class="form-label">Bundle Identifier</label>
                                    <input type="text" 
                                           class="form-control @error('bundle_identifier') is-invalid @enderror" 
                                           id="bundle_identifier" 
                                           name="bundle_identifier" 
                                           value="{{ old('bundle_identifier', $app->bundle_identifier) }}" 
                                           required>
                                    @error('bundle_identifier')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="platform" class="form-label">Platform</label>
                                            <select class="form-select @error('platform') is-invalid @enderror" 
                                                    id="platform" 
                                                    name="platform" 
                                                    required>
                                                <option value="ios" {{ old('platform', $app->platform) == 'ios' ? 'selected' : '' }}>iOS</option>
                                                <option value="android" {{ old('platform', $app->platform) == 'android' ? 'selected' : '' }}>Android</option>
                                                <option value="huawei" {{ old('platform', $app->platform) == 'huawei' ? 'selected' : '' }}>Huawei</option>
                                            </select>
                                            @error('platform')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="sprint_id" class="form-label">Sprint</label>
                                            <select class="form-select @error('sprint_id') is-invalid @enderror" 
                                                    id="sprint_id" 
                                                    name="sprint_id">
                                                <option value="">Select Sprint (Optional)</option>
                                                @foreach($sprints as $sprint)
                                                    <option value="{{ $sprint->id }}" 
                                                            {{ old('sprint_id', $app->sprint_id) == $sprint->id ? 'selected' : '' }}>
                                                        {{ $sprint->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('sprint_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="changelog" class="form-label">Changelog</label>
                                    <textarea class="form-control @error('changelog') is-invalid @enderror" 
                                              id="changelog" 
                                              name="changelog" 
                                              rows="4" 
                                              placeholder="Describe what's new in this version...">{{ old('changelog', $app->changelog) }}</textarea>
                                    @error('changelog')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="ticket_links" class="form-label">Ticket Links</label>
                                    <div id="ticketLinksContainer">
                                        @php
                                            $ticketLinks = is_string($app->ticket_link) ? json_decode($app->ticket_link, true) : $app->ticket_link;
                                            $ticketLinks = is_array($ticketLinks) ? $ticketLinks : [];
                                        @endphp
                                        
                                        @if(empty($ticketLinks))
                                            <div class="ticket-link-row mb-2">
                                                <div class="input-group">
                                                    <input type="url" 
                                                           class="form-control" 
                                                           name="ticket_links[]" 
                                                           placeholder="https://jira.company.com/ticket/123">
                                                    <button type="button" class="btn btn-outline-danger" onclick="removeTicketLink(this)">
                                                        <i class="fas fa-minus"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        @else
                                            @foreach($ticketLinks as $link)
                                                <div class="ticket-link-row mb-2">
                                                    <div class="input-group">
                                                        <input type="url" 
                                                               class="form-control" 
                                                               name="ticket_links[]" 
                                                               value="{{ $link }}"
                                                               placeholder="https://jira.company.com/ticket/123">
                                                        <button type="button" class="btn btn-outline-danger" onclick="removeTicketLink(this)">
                                                            <i class="fas fa-minus"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @endif
                                    </div>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addTicketLink()">
                                        <i class="fas fa-plus me-1"></i>Add Ticket Link
                                    </button>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('apps.show', $app) }}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>Update App
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Current App Info</h6>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                @if($app->icon_path)
                                    <img src="{{ Storage::url($app->icon_path) }}" 
                                         alt="{{ $app->name }}" 
                                         class="img-fluid rounded" 
                                         style="max-width: 80px; max-height: 80px;">
                                @else
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                         style="width: 80px; height: 80px; margin: 0 auto;">
                                        <i class="fas fa-mobile-alt fa-2x text-muted"></i>
                                    </div>
                                @endif
                            </div>
                            
                            <div class="small">
                                <div class="mb-2">
                                    <strong>Platform:</strong>
                                    <span class="badge bg-{{ $app->platform == 'ios' ? 'primary' : ($app->platform == 'android' ? 'success' : 'warning') }}">
                                        {{ ucfirst($app->platform) }}
                                    </span>
                                </div>
                                <div class="mb-2">
                                    <strong>File Size:</strong> {{ number_format($app->file_size / 1024 / 1024, 2) }} MB
                                </div>
                                <div class="mb-2">
                                    <strong>Uploaded:</strong> {{ $app->created_at->format('M j, Y g:i A') }}
                                </div>
                                <div class="mb-2">
                                    <strong>Status:</strong>
                                    <span class="badge bg-{{ $app->status == 'active' ? 'success' : ($app->status == 'quarantined' ? 'warning' : 'secondary') }}">
                                        {{ ucfirst($app->status) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Add ticket link functionality
function addTicketLink() {
    const container = document.getElementById('ticketLinksContainer');
    const newRow = document.createElement('div');
    newRow.className = 'ticket-link-row mb-2';
    newRow.innerHTML = `
        <div class="input-group">
            <input type="url" 
                   class="form-control" 
                   name="ticket_links[]" 
                   placeholder="https://jira.company.com/ticket/123">
            <button type="button" class="btn btn-outline-danger" onclick="removeTicketLink(this)">
                <i class="fas fa-minus"></i>
            </button>
        </div>
    `;
    container.appendChild(newRow);
}

// Remove ticket link functionality
function removeTicketLink(button) {
    const container = document.getElementById('ticketLinksContainer');
    if (container.children.length > 1) {
        button.closest('.ticket-link-row').remove();
    }
}
</script>
@endsection
