@extends('layouts.app')

@section('title', 'User Management')
@section('page-title', 'User Management')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2 text-primary"></i>
                        User Management
                    </h5>
                    <div class="btn-group">
                        @can('users.update')
                        <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#bulkActionModal">
                            <i class="fas fa-tasks me-2"></i>Bulk Actions
                        </button>
                        @endcan
                        @can('users.create')
                        <a href="{{ route('users.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add User
                        </a>
                        @endcan
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Search and Filter -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <form method="GET" action="{{ route('users.index') }}" class="d-flex">
                            <input type="text"
                                   name="search"
                                   class="form-control me-2"
                                   placeholder="Search users..."
                                   value="{{ request('search') }}">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i>
                            </button>
                            @if(request('search'))
                            <a href="{{ route('users.index') }}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-times"></i>
                            </a>
                            @endif
                        </form>
                    </div>
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-4">
                                <select name="role" class="form-select" onchange="filterUsers()">
                                    <option value="">All Roles</option>
                                    @foreach($roles as $role)
                                    <option value="{{ $role->name }}" {{ request('role') === $role->name ? 'selected' : '' }}>
                                        {{ $role->name }}
                                    </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select name="status" class="form-select" onchange="filterUsers()">
                                    <option value="">All Status</option>
                                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    <option value="locked" {{ request('status') === 'locked' ? 'selected' : '' }}>Locked</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <div class="btn-group w-100">
                                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-sort me-2"></i>Sort
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['sort_by' => 'name', 'sort_direction' => 'asc']) }}">Name (A-Z)</a></li>
                                        <li><a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['sort_by' => 'name', 'sort_direction' => 'desc']) }}">Name (Z-A)</a></li>
                                        <li><a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['sort_by' => 'email', 'sort_direction' => 'asc']) }}">Email (A-Z)</a></li>
                                        <li><a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['sort_by' => 'created_at', 'sort_direction' => 'desc']) }}">Newest First</a></li>
                                        <li><a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['sort_by' => 'last_login_at', 'sort_direction' => 'desc']) }}">Last Login</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                @if($users->count() > 0)
                    <!-- Users Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    @can('users.update')
                                    <th width="50">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    @endcan
                                    <th>User</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($users as $user)
                                <tr>
                                    @can('users.update')
                                    <td>
                                        <input type="checkbox" class="form-check-input user-checkbox" value="{{ $user->id }}">
                                    </td>
                                    @endcan
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar me-3">
                                                <div class="bg-{{ $user->is_active ? 'primary' : 'secondary' }} text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                    {{ strtoupper(substr($user->name, 0, 2)) }}
                                                </div>
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ $user->name }}</div>
                                                <small class="text-muted">{{ $user->email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @foreach($user->roles as $role)
                                        <span class="badge bg-{{ $role->name === 'Super Admin' ? 'warning' : ($role->name === 'Admin' ? 'primary' : ($role->name === 'Developer' ? 'info' : 'secondary')) }} me-1">
                                            {{ $role->name }}
                                        </span>
                                        @endforeach
                                        @if($user->roles->isEmpty())
                                        <span class="text-muted">No roles</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($user->isLocked())
                                            <span class="badge bg-danger">
                                                <i class="fas fa-lock me-1"></i>Locked
                                            </span>
                                        @elseif($user->is_active)
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>Active
                                            </span>
                                        @else
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-times me-1"></i>Inactive
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($user->last_login_at)
                                            <small class="text-muted">
                                                {{ $user->last_login_at->diffForHumans() }}
                                            </small>
                                        @else
                                            <small class="text-muted">Never</small>
                                        @endif
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ $user->created_at->format('M d, Y') }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            @can('users.read')
                                            <a href="{{ route('users.show', $user) }}"
                                               class="btn btn-outline-info"
                                               title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @endcan

                                            @can('users.update')
                                            <a href="{{ route('users.edit', $user) }}"
                                               class="btn btn-outline-primary"
                                               title="Edit User">
                                                <i class="fas fa-edit"></i>
                                            </a>

                                            @if($user->id !== auth()->id())
                                            <button type="button"
                                                    class="btn btn-outline-{{ $user->is_active ? 'warning' : 'success' }}"
                                                    title="{{ $user->is_active ? 'Deactivate' : 'Activate' }} User"
                                                    onclick="toggleUserStatus({{ $user->id }}, '{{ $user->name }}', {{ $user->is_active ? 'false' : 'true' }})">
                                                <i class="fas fa-{{ $user->is_active ? 'user-slash' : 'user-check' }}"></i>
                                            </button>
                                            @endif

                                            @if($user->isLocked())
                                            <button type="button"
                                                    class="btn btn-outline-info"
                                                    title="Unlock Account"
                                                    onclick="unlockUser({{ $user->id }}, '{{ $user->name }}')">
                                                <i class="fas fa-unlock"></i>
                                            </button>
                                            @endif
                                            @endcan

                                            @can('users.delete')
                                            @if($user->id !== auth()->id() && !$user->hasRole('Super Admin'))
                                            <button type="button"
                                                    class="btn btn-outline-danger"
                                                    title="Delete User"
                                                    onclick="deleteUser({{ $user->id }}, '{{ $user->name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            @endif
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <small class="text-muted">
                                Showing {{ $users->firstItem() }} to {{ $users->lastItem() }} of {{ $users->total() }} users
                            </small>
                        </div>
                        <div>
                            {{ $users->links() }}
                        </div>
                    </div>
                @else
                    <!-- Empty State -->
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">No Users Found</h4>
                        @if(request('search') || request('role') || request('status'))
                            <p class="text-muted">No users match your search criteria.</p>
                            <a href="{{ route('users.index') }}" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left me-2"></i>View All Users
                            </a>
                        @else
                            <p class="text-muted">Get started by creating your first user.</p>
                            @can('users.create')
                            <a href="{{ route('users.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create First User
                            </a>
                            @endcan
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@can('users.update')
<!-- Bulk Action Modal -->
<div class="modal fade" id="bulkActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-tasks me-2"></i>Bulk Actions
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="bulkActionForm">
                    @csrf
                    <div class="mb-3">
                        <label for="bulk_action" class="form-label">Select Action</label>
                        <select class="form-select" id="bulk_action" name="action" required>
                            <option value="">Choose an action...</option>
                            <option value="activate">Activate Users</option>
                            <option value="deactivate">Deactivate Users</option>
                            @can('users.delete')
                            <option value="delete">Delete Users</option>
                            @endcan
                        </select>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This action will be applied to all selected users.
                        System users (Super Admins) will be automatically excluded from bulk operations.
                    </div>
                    <div id="selectedUsersCount" class="text-muted">
                        No users selected
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="executeBulkAction()" id="bulkActionBtn" disabled>
                    <i class="fas fa-play me-2"></i>Execute Action
                </button>
            </div>
        </div>
    </div>
</div>
@endcan
@endsection

@push('scripts')
<script>
// Filter functionality
function filterUsers() {
    const role = document.querySelector('select[name="role"]').value;
    const status = document.querySelector('select[name="status"]').value;
    const search = document.querySelector('input[name="search"]').value;

    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (role) params.append('role', role);
    if (status) params.append('status', status);

    window.location.href = '{{ route("users.index") }}' + (params.toString() ? '?' + params.toString() : '');
}

// User status toggle
function toggleUserStatus(userId, userName, newStatus) {
    const action = newStatus === 'true' ? 'activate' : 'deactivate';
    const message = `Are you sure you want to ${action} "${userName}"?`;

    if (confirm(message)) {
        fetch(`/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => window.location.reload(), 1500);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'An error occurred while updating user status.');
        });
    }
}

// Delete user
function deleteUser(userId, userName) {
    if (confirm(`Are you sure you want to delete "${userName}"? This action cannot be undone.`)) {
        fetch(`/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => window.location.reload(), 1500);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'An error occurred while deleting the user.');
        });
    }
}

// Show alert function
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.querySelector('.card-body').insertAdjacentHTML('afterbegin', alertHtml);
}
</script>
@endpush
