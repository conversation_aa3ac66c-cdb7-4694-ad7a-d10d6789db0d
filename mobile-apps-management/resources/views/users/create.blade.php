@extends('layouts.app')

@section('title', 'Create User')
@section('page-title', 'Create New User')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-user-plus me-2 text-primary"></i>
                        Create New User
                    </h5>
                    <a href="{{ route('users.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Users
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('users.store') }}" id="createUserForm">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- User Information -->
                            <div class="card border">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <i class="fas fa-user me-2"></i>User Information
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <!-- Full Name -->
                                            <div class="mb-3">
                                                <label for="name" class="form-label">
                                                    Full Name <span class="text-danger">*</span>
                                                </label>
                                                <input type="text" 
                                                       class="form-control @error('name') is-invalid @enderror" 
                                                       id="name" 
                                                       name="name" 
                                                       value="{{ old('name') }}" 
                                                       placeholder="Enter full name"
                                                       required>
                                                @error('name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <!-- Email -->
                                            <div class="mb-3">
                                                <label for="email" class="form-label">
                                                    Email Address <span class="text-danger">*</span>
                                                </label>
                                                <input type="email" 
                                                       class="form-control @error('email') is-invalid @enderror" 
                                                       id="email" 
                                                       name="email" 
                                                       value="{{ old('email') }}" 
                                                       placeholder="Enter email address"
                                                       required>
                                                @error('email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <!-- Password -->
                                            <div class="mb-3">
                                                <label for="password" class="form-label">
                                                    Password <span class="text-danger">*</span>
                                                </label>
                                                <input type="password" 
                                                       class="form-control @error('password') is-invalid @enderror" 
                                                       id="password" 
                                                       name="password" 
                                                       placeholder="Enter password"
                                                       required>
                                                @error('password')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">
                                                    Minimum {{ config('security.authentication.password_min_length', 8) }} characters
                                                    @if(config('security.authentication.require_password_complexity', true))
                                                    with uppercase, lowercase, numbers, and symbols
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <!-- Confirm Password -->
                                            <div class="mb-3">
                                                <label for="password_confirmation" class="form-label">
                                                    Confirm Password <span class="text-danger">*</span>
                                                </label>
                                                <input type="password" 
                                                       class="form-control @error('password_confirmation') is-invalid @enderror" 
                                                       id="password_confirmation" 
                                                       name="password_confirmation" 
                                                       placeholder="Confirm password"
                                                       required>
                                                @error('password_confirmation')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Account Status -->
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   id="is_active" 
                                                   name="is_active" 
                                                   value="1"
                                                   {{ old('is_active', true) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="is_active">
                                                <strong>Active Account</strong>
                                                <small class="text-muted d-block">User can log in and access the system</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Role Assignment -->
                            <div class="card border">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <i class="fas fa-user-shield me-2"></i>Role Assignment
                                    </h6>
                                </div>
                                <div class="card-body">
                                    @if($roles->count() > 0)
                                        @foreach($roles as $role)
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   name="roles[]" 
                                                   value="{{ $role->name }}" 
                                                   id="role_{{ $role->id }}"
                                                   {{ in_array($role->name, old('roles', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="role_{{ $role->id }}">
                                                <div class="d-flex align-items-center">
                                                    @if($role->name === 'Super Admin')
                                                        <i class="fas fa-crown text-warning me-2"></i>
                                                    @elseif($role->name === 'Admin')
                                                        <i class="fas fa-user-shield text-primary me-2"></i>
                                                    @elseif($role->name === 'Developer')
                                                        <i class="fas fa-code text-info me-2"></i>
                                                    @else
                                                        <i class="fas fa-user-tag text-secondary me-2"></i>
                                                    @endif
                                                    <div>
                                                        <strong>{{ $role->name }}</strong>
                                                        <small class="text-muted d-block">{{ $role->permissions->count() }} permissions</small>
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                        @endforeach
                                    @else
                                        <p class="text-muted">No roles available</p>
                                    @endif
                                </div>
                            </div>

                            <!-- User Summary -->
                            <div class="card border mt-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>Summary
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div id="userSummary">
                                        <p class="text-muted mb-2">Selected Roles: <span id="roleCount">0</span></p>
                                        <div id="selectedRoles" class="d-flex flex-wrap gap-1"></div>
                                        <hr>
                                        <div class="small text-muted">
                                            <div class="d-flex justify-content-between">
                                                <span>Account Status:</span>
                                                <span id="accountStatus" class="badge bg-success">Active</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ route('users.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save me-2"></i>
                            <span class="btn-text">Create User</span>
                            <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createUserForm');
    const submitBtn = document.getElementById('submitBtn');
    const roleCheckboxes = document.querySelectorAll('input[name="roles[]"]');
    const isActiveCheckbox = document.getElementById('is_active');

    // Update user summary when roles change
    function updateUserSummary() {
        const checkedRoles = document.querySelectorAll('input[name="roles[]"]:checked');
        const roleCount = document.getElementById('roleCount');
        const selectedRoles = document.getElementById('selectedRoles');
        const accountStatus = document.getElementById('accountStatus');
        
        roleCount.textContent = checkedRoles.length;
        
        selectedRoles.innerHTML = '';
        checkedRoles.forEach(checkbox => {
            const badge = document.createElement('span');
            badge.className = 'badge bg-primary me-1 mb-1';
            badge.textContent = checkbox.value;
            selectedRoles.appendChild(badge);
        });
        
        // Update account status
        if (isActiveCheckbox.checked) {
            accountStatus.textContent = 'Active';
            accountStatus.className = 'badge bg-success';
        } else {
            accountStatus.textContent = 'Inactive';
            accountStatus.className = 'badge bg-secondary';
        }
    }

    // Role checkbox change
    roleCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateUserSummary);
    });

    // Active status change
    isActiveCheckbox.addEventListener('change', updateUserSummary);

    // Form submission
    form.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.querySelector('.btn-text').textContent = 'Creating...';
        submitBtn.querySelector('.spinner-border').classList.remove('d-none');
    });

    // Password confirmation validation
    const passwordField = document.getElementById('password');
    const confirmField = document.getElementById('password_confirmation');
    
    confirmField.addEventListener('input', function() {
        if (this.value && this.value !== passwordField.value) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });

    // Initialize summary
    updateUserSummary();
});
</script>
@endpush
