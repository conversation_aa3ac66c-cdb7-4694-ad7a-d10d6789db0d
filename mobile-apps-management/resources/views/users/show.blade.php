@extends('layouts.app')

@section('title', 'User Details')
@section('page-title', 'User: ' . $user->name)

@section('content')
<div class="row">
    <!-- User Information -->
    <div class="col-md-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2 text-primary"></i>
                        User Details
                    </h5>
                    <div class="btn-group">
                        @can('users.update')
                        <a href="{{ route('users.edit', $user) }}" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>Edit User
                        </a>
                        @endcan
                        <a href="{{ route('users.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Users
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- User Basic Info -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <div class="avatar me-3">
                                <div class="bg-{{ $user->is_active ? 'primary' : 'secondary' }} text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; font-size: 1.5rem;">
                                    {{ strtoupper(substr($user->name, 0, 2)) }}
                                </div>
                            </div>
                            <div>
                                <h4 class="mb-1">{{ $user->name }}</h4>
                                <p class="text-muted mb-1">{{ $user->email }}</p>
                                @if($user->isLocked())
                                    <span class="badge bg-danger">
                                        <i class="fas fa-lock me-1"></i>Account Locked
                                    </span>
                                @elseif($user->is_active)
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Active
                                    </span>
                                @else
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-times me-1"></i>Inactive
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="border-end">
                                    <h5 class="text-primary mb-0">{{ $stats['apps_uploaded'] }}</h5>
                                    <small class="text-muted">Apps Uploaded</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h5 class="text-info mb-0">{{ $stats['sprints_created'] }}</h5>
                                    <small class="text-muted">Sprints Created</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h5 class="text-success mb-0">{{ $stats['total_logins'] }}</h5>
                                <small class="text-muted">Total Logins</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Roles -->
                <div class="mb-4">
                    <h6 class="mb-3">
                        <i class="fas fa-user-shield me-2"></i>Assigned Roles
                    </h6>
                    @if($user->roles->count() > 0)
                        <div class="row">
                            @foreach($user->roles as $role)
                            <div class="col-md-6 mb-3">
                                <div class="card border">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                @if($role->name === 'Super Admin')
                                                    <i class="fas fa-crown text-warning fa-2x"></i>
                                                @elseif($role->name === 'Admin')
                                                    <i class="fas fa-user-shield text-primary fa-2x"></i>
                                                @elseif($role->name === 'Developer')
                                                    <i class="fas fa-code text-info fa-2x"></i>
                                                @else
                                                    <i class="fas fa-user-tag text-secondary fa-2x"></i>
                                                @endif
                                            </div>
                                            <div>
                                                <h6 class="mb-1">{{ $role->name }}</h6>
                                                <small class="text-muted">{{ $role->permissions->count() }} permissions</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-user-shield fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No roles assigned to this user.</p>
                        </div>
                    @endif
                </div>

                <!-- Account Information -->
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Account Created</h6>
                        <p>{{ $user->created_at->format('F d, Y \a\t g:i A') }}</p>
                        
                        <h6 class="text-muted">Last Updated</h6>
                        <p>{{ $user->updated_at->format('F d, Y \a\t g:i A') }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Last Login</h6>
                        <p>
                            @if($user->last_login_at)
                                {{ $user->last_login_at->format('F d, Y \a\t g:i A') }}
                                <small class="text-muted">({{ $user->last_login_at->diffForHumans() }})</small>
                            @else
                                <span class="text-muted">Never logged in</span>
                            @endif
                        </p>
                        
                        @if($user->password_changed_at)
                        <h6 class="text-muted">Password Last Changed</h6>
                        <p>
                            {{ $user->password_changed_at->format('F d, Y \a\t g:i A') }}
                            @if($user->needsPasswordChange())
                                <span class="badge bg-warning ms-2">Expired</span>
                            @endif
                        </p>
                        @endif
                    </div>
                </div>

                <!-- Security Information -->
                @if($user->failed_login_attempts > 0 || $user->isLocked())
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Security Information</h6>
                    @if($user->failed_login_attempts > 0)
                    <p class="mb-1">Failed login attempts: <strong>{{ $user->failed_login_attempts }}</strong></p>
                    @endif
                    @if($user->isLocked())
                    <p class="mb-0">Account locked until: <strong>{{ $user->locked_until->format('F d, Y \a\t g:i A') }}</strong></p>
                    @endif
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Activity & Actions -->
    <div class="col-md-4">
        <!-- Quick Actions -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-0">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    @can('users.update')
                    @if($user->id !== auth()->id())
                    <button type="button" 
                            class="btn btn-outline-{{ $user->is_active ? 'warning' : 'success' }}"
                            onclick="toggleUserStatus({{ $user->id }}, '{{ $user->name }}', {{ $user->is_active ? 'false' : 'true' }})">
                        <i class="fas fa-{{ $user->is_active ? 'user-slash' : 'user-check' }} me-2"></i>
                        {{ $user->is_active ? 'Deactivate' : 'Activate' }} Account
                    </button>
                    @endif

                    @if($user->isLocked())
                    <button type="button" 
                            class="btn btn-outline-info"
                            onclick="unlockUser({{ $user->id }}, '{{ $user->name }}')">
                        <i class="fas fa-unlock me-2"></i>Unlock Account
                    </button>
                    @endif

                    <button type="button" 
                            class="btn btn-outline-warning"
                            onclick="forcePasswordReset({{ $user->id }}, '{{ $user->name }}')">
                        <i class="fas fa-key me-2"></i>Force Password Reset
                    </button>
                    @endcan

                    @can('users.read')
                    <button type="button" 
                            class="btn btn-outline-secondary"
                            onclick="loadUserActivity({{ $user->id }})">
                        <i class="fas fa-history me-2"></i>View Activity
                    </button>
                    @endcan
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h6 class="mb-0">
                    <i class="fas fa-history me-2"></i>Recent Activity
                </h6>
            </div>
            <div class="card-body">
                @if($recentActivity->count() > 0)
                    <div class="timeline">
                        @foreach($recentActivity->take(5) as $activity)
                        <div class="timeline-item mb-3">
                            <div class="d-flex">
                                <div class="timeline-marker me-3">
                                    <i class="fas fa-{{ $activity->getSeverityIcon() }} text-{{ $activity->getSeverityColor() }}"></i>
                                </div>
                                <div class="timeline-content">
                                    <div class="fw-bold">{{ $activity->event }}</div>
                                    <small class="text-muted">{{ $activity->description }}</small>
                                    <div class="text-muted small">{{ $activity->created_at->diffForHumans() }}</div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    @if($recentActivity->count() > 5)
                    <button type="button" class="btn btn-sm btn-outline-primary w-100" onclick="loadUserActivity({{ $user->id }})">
                        View All Activity
                    </button>
                    @endif
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent activity</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// User status toggle
function toggleUserStatus(userId, userName, newStatus) {
    const action = newStatus === 'true' ? 'activate' : 'deactivate';
    const message = `Are you sure you want to ${action} "${userName}"?`;
    
    if (confirm(message)) {
        fetch(`/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => window.location.reload(), 1500);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'An error occurred while updating user status.');
        });
    }
}

// Unlock user
function unlockUser(userId, userName) {
    if (confirm(`Unlock account for "${userName}"?`)) {
        fetch(`/users/${userId}/unlock`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => window.location.reload(), 1500);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'An error occurred while unlocking the user.');
        });
    }
}

// Force password reset
function forcePasswordReset(userId, userName) {
    if (confirm(`Force password reset for "${userName}"? They will be required to change their password on next login.`)) {
        fetch(`/users/${userId}/force-password-reset`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'An error occurred while forcing password reset.');
        });
    }
}

// Load user activity
function loadUserActivity(userId) {
    // This would open a modal or navigate to a detailed activity page
    window.open(`/users/${userId}/activity`, '_blank');
}

// Show alert function
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.querySelector('.card-body').insertAdjacentHTML('afterbegin', alertHtml);
}
</script>
@endpush
