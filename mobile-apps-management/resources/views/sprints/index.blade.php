@extends('layouts.app')

@section('title', 'Sprint Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Sprint Management</h1>
                    <p class="text-muted">Manage development sprints and releases</p>
                </div>
                <div class="btn-group">
                    @if($showDeleted)
                        <a href="{{ route('sprints.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-1"></i>Show Active
                        </a>
                    @else
                        <a href="{{ route('sprints.index', ['show_deleted' => 1]) }}" class="btn btn-outline-warning">
                            <i class="fas fa-trash me-1"></i>Show Deleted
                        </a>
                    @endif

                    @can('sprints.create')
                    @if(!$showDeleted)
                    <a href="{{ route('sprints.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Create Sprint
                    </a>
                    @endif
                    @endcan
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tasks text-primary me-2"></i>
                        {{ $showDeleted ? 'Deleted Sprints' : 'All Sprints' }}
                    </h5>
                </div>
                <div class="card-body">
                    @if($sprints->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Sprint Name</th>
                                        <th>Status</th>
                                        <th>Duration</th>
                                        <th>Apps Count</th>
                                        <th>Created By</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sprints as $sprint)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $sprint->name }}</strong>
                                                @if($sprint->description)
                                                    <br><small class="text-muted">{{ Str::limit($sprint->description, 50) }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $sprint->getStatusColor() }}">
                                                <i class="fas fa-{{ $sprint->getStatusIcon() }} me-1"></i>
                                                {{ ucfirst($sprint->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($sprint->start_date && $sprint->end_date)
                                                <div class="small">
                                                    <strong>Start:</strong> {{ $sprint->start_date->format('M j, Y') }}<br>
                                                    <strong>End:</strong> {{ $sprint->end_date->format('M j, Y') }}
                                                </div>
                                            @elseif($sprint->start_date)
                                                <div class="small">
                                                    <strong>Start:</strong> {{ $sprint->start_date->format('M j, Y') }}
                                                </div>
                                            @else
                                                <span class="text-muted">Not scheduled</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $sprint->apps_count }} apps</span>
                                        </td>
                                        <td>{{ $sprint->creator->name ?? 'Unknown' }}</td>
                                        <td>
                                            <small class="text-muted">
                                                {{ $sprint->created_at->diffForHumans() }}
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                @if($showDeleted)
                                                    @can('sprints.update')
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-success"
                                                            title="Restore Sprint"
                                                            onclick="confirmRestore('{{ $sprint->name }}', '{{ route('sprints.restore', $sprint->id) }}')">
                                                        <i class="fas fa-undo"></i>
                                                    </button>
                                                    @endcan
                                                @else
                                                    @can('sprints.read')
                                                    <a href="{{ route('sprints.show', $sprint) }}"
                                                       class="btn btn-sm btn-outline-primary"
                                                       title="View Sprint">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @endcan

                                                    @can('sprints.update')
                                                    <a href="{{ route('sprints.edit', $sprint) }}"
                                                       class="btn btn-sm btn-outline-secondary"
                                                       title="Edit Sprint">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    @endcan

                                                    @can('sprints.delete')
                                                    @if($sprint->canBeDeleted())
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-danger"
                                                            title="Delete Sprint"
                                                            onclick="confirmDelete('{{ $sprint->name }}', '{{ route('sprints.destroy', $sprint) }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    @else
                                                    <span class="btn btn-sm btn-outline-secondary disabled" title="Sprint must be cancelled and have no apps to delete">
                                                        <i class="fas fa-lock"></i>
                                                    </span>
                                                    @endif
                                                    @endcan
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $sprints->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Sprints Found</h5>
                            <p class="text-muted">Get started by creating your first sprint.</p>
                            @can('sprints.create')
                            <a href="{{ route('sprints.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>Create First Sprint
                            </a>
                            @endcan
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the sprint <strong id="deleteSprintName"></strong>?</p>
                <p class="text-danger small">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete Sprint</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(sprintName, deleteUrl) {
    document.getElementById('deleteSprintName').textContent = sprintName;
    document.getElementById('deleteForm').action = deleteUrl;

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function confirmRestore(sprintName, restoreUrl) {
    if (confirm(`Are you sure you want to restore the sprint "${sprintName}"? It will be set to planning status.`)) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = restoreUrl;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        form.appendChild(csrfToken);

        // Add method override
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'PATCH';
        form.appendChild(methodField);

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endsection
