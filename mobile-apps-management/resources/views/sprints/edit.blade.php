@extends('layouts.app')

@section('title', 'Edit Sprint')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Edit Sprint</h1>
                    <p class="text-muted">Update sprint information and settings</p>
                </div>
                <a href="{{ route('sprints.show', $sprint) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Sprint
                </a>
            </div>

            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-edit text-primary me-2"></i>
                                Sprint Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('sprints.update', $sprint) }}" method="POST">
                                @csrf
                                @method('PUT')

                                <div class="mb-3">
                                    <label for="name" class="form-label">Sprint Name <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control @error('name') is-invalid @enderror" 
                                           id="name" 
                                           name="name" 
                                           value="{{ old('name', $sprint->name) }}" 
                                           placeholder="e.g., Sprint 2024.1, Release v2.0"
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" 
                                              name="description" 
                                              rows="3" 
                                              placeholder="Describe the goals and scope of this sprint...">{{ old('description', $sprint->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="start_date" class="form-label">Start Date</label>
                                            <input type="date" 
                                                   class="form-control @error('start_date') is-invalid @enderror" 
                                                   id="start_date" 
                                                   name="start_date" 
                                                   value="{{ old('start_date', $sprint->start_date?->format('Y-m-d')) }}">
                                            @error('start_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="end_date" class="form-label">End Date</label>
                                            <input type="date" 
                                                   class="form-control @error('end_date') is-invalid @enderror" 
                                                   id="end_date" 
                                                   name="end_date" 
                                                   value="{{ old('end_date', $sprint->end_date?->format('Y-m-d')) }}">
                                            @error('end_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select @error('status') is-invalid @enderror" 
                                            id="status" 
                                            name="status" 
                                            required>
                                        <option value="">Select Status</option>
                                        <option value="planning" {{ old('status', $sprint->status) == 'planning' ? 'selected' : '' }}>Planning</option>
                                        <option value="active" {{ old('status', $sprint->status) == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="completed" {{ old('status', $sprint->status) == 'completed' ? 'selected' : '' }}>Completed</option>
                                        <option value="cancelled" {{ old('status', $sprint->status) == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        <small class="text-muted">
                                            Note: Completed sprints cannot be reactivated. Cancelled sprints can be restored to planning status.
                                        </small>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('sprints.show', $sprint) }}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>Update Sprint
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Current Sprint Info</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Current Status</label>
                                <div>
                                    <span class="badge bg-{{ $sprint->getStatusColor() }} fs-6">
                                        <i class="fas fa-{{ $sprint->getStatusIcon() }} me-1"></i>
                                        {{ ucfirst($sprint->status) }}
                                    </span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Apps Count</label>
                                <p class="text-muted">{{ $sprint->apps->count() }} apps</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Created</label>
                                <p class="text-muted">{{ $sprint->created_at->format('M j, Y g:i A') }}</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Created By</label>
                                <p class="text-muted">{{ $sprint->creator->name ?? 'Unknown' }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">Status Guidelines</h6>
                        </div>
                        <div class="card-body">
                            <div class="small">
                                <div class="mb-2">
                                    <span class="badge bg-secondary me-2">Planning</span>
                                    Sprint is being planned
                                </div>
                                <div class="mb-2">
                                    <span class="badge bg-success me-2">Active</span>
                                    Sprint is currently running
                                </div>
                                <div class="mb-2">
                                    <span class="badge bg-primary me-2">Completed</span>
                                    Sprint finished successfully
                                </div>
                                <div class="mb-2">
                                    <span class="badge bg-danger me-2">Cancelled</span>
                                    Sprint was cancelled
                                </div>
                                
                                <hr class="my-3">
                                
                                <div class="text-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    <strong>Important:</strong>
                                    <ul class="mt-2 mb-0">
                                        <li>Completed sprints cannot be reactivated</li>
                                        <li>Cancelled sprints can be restored to planning</li>
                                        <li>Only cancelled sprints with no apps can be deleted</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
