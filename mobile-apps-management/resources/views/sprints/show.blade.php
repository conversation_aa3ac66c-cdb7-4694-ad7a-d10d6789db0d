@extends('layouts.app')

@section('title', 'Sprint Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">{{ $sprint->name }}</h1>
                    <p class="text-muted">Sprint details and associated apps</p>
                </div>
                <div class="btn-group">
                    <a href="{{ route('sprints.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Sprints
                    </a>
                    @can('sprints.update')
                    <a href="{{ route('sprints.edit', $sprint) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-1"></i>Edit Sprint
                    </a>
                    @endcan
                </div>
            </div>



            <div class="row">
                <!-- Sprint Information -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle text-primary me-2"></i>
                                Sprint Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Status</label>
                                <div>
                                    <span class="badge bg-{{ $sprint->getStatusColor() }} fs-6">
                                        <i class="fas fa-{{ $sprint->getStatusIcon() }} me-1"></i>
                                        {{ ucfirst($sprint->status) }}
                                    </span>
                                </div>
                            </div>

                            @if($sprint->description)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Description</label>
                                <p class="text-muted">{{ $sprint->description }}</p>
                            </div>
                            @endif

                            @if($sprint->start_date)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Start Date</label>
                                <p class="text-muted">{{ $sprint->start_date->format('F j, Y') }}</p>
                            </div>
                            @endif

                            @if($sprint->end_date)
                            <div class="mb-3">
                                <label class="form-label fw-bold">End Date</label>
                                <p class="text-muted">{{ $sprint->end_date->format('F j, Y') }}</p>
                            </div>
                            @endif

                            <div class="mb-3">
                                <label class="form-label fw-bold">Created By</label>
                                <p class="text-muted">{{ $sprint->creator->name ?? 'Unknown' }}</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Created</label>
                                <p class="text-muted">{{ $sprint->created_at->format('F j, Y g:i A') }}</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Apps Count</label>
                                <p class="text-muted">{{ $sprint->apps->count() }} apps</p>
                            </div>

                            @if($sprint->apps->count() > 0)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Total Size</label>
                                <p class="text-muted">{{ number_format($sprint->apps->sum('file_size') / 1024 / 1024, 2) }} MB</p>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Sprint Actions -->
                    @can('sprints.update')
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">Sprint Actions</h6>
                        </div>
                        <div class="card-body">
                            @if($sprint->status === 'cancelled')
                                <button class="btn btn-success btn-sm w-100 mb-2" onclick="updateSprintStatus('{{ $sprint->id }}', 'planning')">
                                    <i class="fas fa-undo me-1"></i>Restore Sprint
                                </button>
                            @elseif($sprint->status !== 'completed')
                                <button class="btn btn-warning btn-sm w-100 mb-2" onclick="updateSprintStatus('{{ $sprint->id }}', 'cancelled')">
                                    <i class="fas fa-ban me-1"></i>Cancel Sprint
                                </button>
                            @endif

                            @if($sprint->status === 'active')
                                <button class="btn btn-primary btn-sm w-100 mb-2" onclick="updateSprintStatus('{{ $sprint->id }}', 'completed')">
                                    <i class="fas fa-check me-1"></i>Mark as Completed
                                </button>
                            @endif

                            @can('sprints.delete')
                            @if($sprint->status === 'cancelled' && $sprint->apps->count() === 0)
                                <button class="btn btn-danger btn-sm w-100" onclick="confirmDelete('{{ $sprint->name }}', '{{ route('sprints.destroy', $sprint) }}')">
                                    <i class="fas fa-trash me-1"></i>Delete Sprint
                                </button>
                            @endif
                            @endcan
                        </div>
                    </div>
                    @endcan
                </div>

                <!-- Apps in Sprint -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-mobile-alt text-primary me-2"></i>
                                Apps in Sprint ({{ $sprint->apps->count() }})
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($sprint->apps->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>App</th>
                                                <th>Platform</th>
                                                <th>Version + Build</th>
                                                <th>Size</th>
                                                <th>Uploaded</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($sprint->apps as $app)
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        @if($app->icon_path)
                                                            <img src="{{ Storage::url($app->icon_path) }}"
                                                                 alt="{{ $app->name }}"
                                                                 class="rounded me-2"
                                                                 style="width: 32px; height: 32px;">
                                                        @else
                                                            <i class="{{ $app->getPlatformIcon() }} text-{{ $app->getPlatformColor() }} me-2 fa-lg"></i>
                                                        @endif
                                                        <div>
                                                            <strong>{{ $app->name }}</strong>
                                                            <br><small class="text-muted">{{ $app->bundle_identifier }}</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-{{ $app->getPlatformColor() }}">
                                                        {{ ucfirst($app->platform) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong>{{ $app->version }}</strong>
                                                        @if($app->build_number)
                                                            <small class="text-muted">({{ $app->build_number }})</small>
                                                        @endif
                                                    </div>
                                                </td>
                                                <td>{{ number_format($app->file_size / 1024 / 1024, 2) }} MB</td>
                                                <td>
                                                    <small class="text-muted">
                                                        {{ $app->created_at->diffForHumans() }}
                                                    </small>
                                                </td>
                                                <td>
                                                    <a href="{{ route('apps.show', $app) }}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <i class="fas fa-mobile-alt fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No Apps in Sprint</h5>
                                    <p class="text-muted">Apps will appear here when they are uploaded and assigned to this sprint.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to permanently delete the sprint <strong id="deleteSprintName"></strong>?</p>
                <p class="text-danger small">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete Sprint</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(sprintName, deleteUrl) {
    document.getElementById('deleteSprintName').textContent = sprintName;
    document.getElementById('deleteForm').action = deleteUrl;

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function updateSprintStatus(sprintId, newStatus) {
    if (confirm(`Are you sure you want to change the sprint status to ${newStatus}?`)) {
        fetch(`/sprints/${sprintId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: newStatus })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to update sprint status: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error updating sprint status');
        });
    }
}
</script>
@endsection
