@extends('layouts.app')

@section('title', 'Role Details')
@section('page-title', 'Role: ' . $role->name)

@section('content')
<div class="row">
    <!-- Role Information -->
    <div class="col-md-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-user-shield me-2 text-primary"></i>
                        Role Details
                    </h5>
                    <div class="btn-group">
                        @can('roles.update')
                        @if(!in_array($role->name, ['Super Admin']))
                        <a href="{{ route('roles.edit', $role) }}" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>Edit Role
                        </a>
                        @endif
                        @endcan
                        <a href="{{ route('roles.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Roles
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Role Basic Info -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <div class="role-icon me-3">
                                @if($role->name === 'Super Admin')
                                    <i class="fas fa-crown text-warning fa-2x"></i>
                                @elseif($role->name === 'Admin')
                                    <i class="fas fa-user-shield text-primary fa-2x"></i>
                                @elseif($role->name === 'Developer')
                                    <i class="fas fa-code text-info fa-2x"></i>
                                @else
                                    <i class="fas fa-user-tag text-secondary fa-2x"></i>
                                @endif
                            </div>
                            <div>
                                <h4 class="mb-1">{{ $role->name }}</h4>
                                @if(in_array($role->name, ['Super Admin', 'Admin']))
                                    <span class="badge bg-warning">System Role</span>
                                @else
                                    <span class="badge bg-success">Custom Role</span>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h5 class="text-primary mb-0">{{ $role->users->count() }}</h5>
                                    <small class="text-muted">Users</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h5 class="text-info mb-0">{{ $role->permissions->count() }}</h5>
                                <small class="text-muted">Permissions</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Permissions by Module -->
                <div class="mb-4">
                    <h6 class="mb-3">
                        <i class="fas fa-shield-alt me-2"></i>Permissions by Module
                    </h6>
                    @if($permissionsByModule->count() > 0)
                        <div class="row">
                            @foreach($permissionsByModule as $module => $permissions)
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card border">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">
                                            <i class="fas fa-{{ $module === 'users' ? 'users' : ($module === 'roles' ? 'user-shield' : ($module === 'apps' ? 'mobile-alt' : ($module === 'sprints' ? 'calendar-alt' : 'tachometer-alt'))) }} me-2"></i>
                                            {{ ucfirst($module) }}
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        @foreach($permissions as $permission)
                                        <span class="badge bg-{{ explode('.', $permission->name)[1] === 'create' ? 'success' : (explode('.', $permission->name)[1] === 'read' ? 'info' : (explode('.', $permission->name)[1] === 'update' ? 'warning' : 'danger')) }} me-1 mb-1">
                                            {{ strtoupper(explode('.', $permission->name)[1]) }}
                                        </span>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
                            <p class="text-muted">This role has no permissions assigned.</p>
                        </div>
                    @endif
                </div>

                <!-- Role Metadata -->
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Created Date</h6>
                        <p>{{ $role->created_at->format('F d, Y \a\t g:i A') }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Last Updated</h6>
                        <p>{{ $role->updated_at->format('F d, Y \a\t g:i A') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users with this Role -->
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        Users ({{ $role->users->count() }})
                    </h6>
                    @can('roles.update')
                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#assignUserModal">
                        <i class="fas fa-plus"></i>
                    </button>
                    @endcan
                </div>
            </div>
            <div class="card-body">
                @if($users->count() > 0)
                    @foreach($users as $user)
                    <div class="d-flex justify-content-between align-items-center mb-3 p-2 border rounded">
                        <div class="d-flex align-items-center">
                            <div class="avatar me-3">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    {{ strtoupper(substr($user->name, 0, 2)) }}
                                </div>
                            </div>
                            <div>
                                <div class="fw-bold">{{ $user->name }}</div>
                                <small class="text-muted">{{ $user->email }}</small>
                            </div>
                        </div>
                        @can('roles.update')
                        @if(!in_array($role->name, ['Super Admin']) || $role->users()->count() > 1)
                        <button class="btn btn-sm btn-outline-danger" 
                                onclick="removeUserFromRole({{ $user->id }}, '{{ $user->name }}', {{ $role->id }}, '{{ $role->name }}')"
                                title="Remove from role">
                            <i class="fas fa-times"></i>
                        </button>
                        @endif
                        @endcan
                    </div>
                    @endforeach

                    @if($users->hasPages())
                    <div class="mt-3">
                        {{ $users->links() }}
                    </div>
                    @endif
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No users assigned to this role.</p>
                        @can('roles.update')
                        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#assignUserModal">
                            <i class="fas fa-plus me-2"></i>Assign User
                        </button>
                        @endcan
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@can('roles.update')
<!-- Assign User Modal -->
<div class="modal fade" id="assignUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>Assign User to Role
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="assignUserForm">
                    @csrf
                    <div class="mb-3">
                        <label for="user_id" class="form-label">Select User</label>
                        <select class="form-select" id="user_id" name="user_id" required>
                            <option value="">Choose a user...</option>
                            @foreach(\App\Models\User::where('is_active', true)->whereDoesntHave('roles', function($q) use ($role) { $q->where('id', $role->id); })->get() as $availableUser)
                            <option value="{{ $availableUser->id }}">{{ $availableUser->name }} ({{ $availableUser->email }})</option>
                            @endforeach
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="assignUserToRole()">
                    <i class="fas fa-save me-2"></i>Assign Role
                </button>
            </div>
        </div>
    </div>
</div>
@endcan
@endsection

@push('scripts')
<script>
@can('roles.update')
function assignUserToRole() {
    const userId = document.getElementById('user_id').value;
    if (!userId) {
        alert('Please select a user.');
        return;
    }

    fetch(`/roles/{{ $role->id }}/assign-user`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ user_id: userId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => window.location.reload(), 1500);
        } else {
            showAlert('danger', data.message);
        }
        bootstrap.Modal.getInstance(document.getElementById('assignUserModal')).hide();
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'An error occurred while assigning the role.');
    });
}

function removeUserFromRole(userId, userName, roleId, roleName) {
    if (confirm(`Remove "${userName}" from the "${roleName}" role?`)) {
        fetch(`/roles/${roleId}/remove-user`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ user_id: userId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => window.location.reload(), 1500);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'An error occurred while removing the user from role.');
        });
    }
}
@endcan

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.querySelector('.card-body').insertAdjacentHTML('afterbegin', alertHtml);
}
</script>
@endpush
