@extends('layouts.app')

@section('title', 'Roles & Permissions')
@section('page-title', 'Roles & Permissions')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-user-shield me-2 text-primary"></i>
                        Roles & Permissions Management
                    </h5>
                    @can('roles.create')
                    <a href="{{ route('roles.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Role
                    </a>
                    @endcan
                </div>
            </div>
            <div class="card-body">
                <!-- Search and Filter -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <form method="GET" action="{{ route('roles.index') }}" class="d-flex">
                            <input type="text"
                                   name="search"
                                   class="form-control me-2"
                                   placeholder="Search roles..."
                                   value="{{ request('search') }}">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i>
                            </button>
                            @if(request('search'))
                            <a href="{{ route('roles.index') }}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-times"></i>
                            </a>
                            @endif
                        </form>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-sort me-2"></i>Sort By
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['sort_by' => 'name', 'sort_direction' => 'asc']) }}">Name (A-Z)</a></li>
                                <li><a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['sort_by' => 'name', 'sort_direction' => 'desc']) }}">Name (Z-A)</a></li>
                                <li><a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['sort_by' => 'users_count', 'sort_direction' => 'desc']) }}">Most Users</a></li>
                                <li><a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['sort_by' => 'permissions_count', 'sort_direction' => 'desc']) }}">Most Permissions</a></li>
                                <li><a class="dropdown-item" href="{{ request()->fullUrlWithQuery(['sort_by' => 'created_at', 'sort_direction' => 'desc']) }}">Newest First</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                @if($roles->count() > 0)
                    <!-- Roles Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Role Name</th>
                                    <th>Users</th>
                                    <th>Permissions</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($roles as $role)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="role-icon me-3">
                                                @if($role->name === 'Super Admin')
                                                    <i class="fas fa-crown text-warning"></i>
                                                @elseif($role->name === 'Admin')
                                                    <i class="fas fa-user-shield text-primary"></i>
                                                @elseif($role->name === 'Developer')
                                                    <i class="fas fa-code text-info"></i>
                                                @else
                                                    <i class="fas fa-user-tag text-secondary"></i>
                                                @endif
                                            </div>
                                            <div>
                                                <strong>{{ $role->name }}</strong>
                                                @if(in_array($role->name, ['Super Admin', 'Admin']))
                                                    <span class="badge bg-warning ms-2">System</span>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ $role->users_count }}</span>
                                        @if($role->users_count > 0)
                                            <small class="text-muted">users</small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $role->permissions_count }}</span>
                                        @if($role->permissions_count > 0)
                                            <small class="text-muted">permissions</small>
                                        @endif
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ $role->created_at->format('M d, Y') }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            @can('roles.read')
                                            <a href="{{ route('roles.show', $role) }}"
                                               class="btn btn-outline-info"
                                               title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @endcan

                                            @can('roles.update')
                                            @if(!in_array($role->name, ['Super Admin']))
                                            <a href="{{ route('roles.edit', $role) }}"
                                               class="btn btn-outline-primary"
                                               title="Edit Role">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @endif
                                            @endcan

                                            @can('roles.delete')
                                            @if(!in_array($role->name, ['Super Admin', 'Admin']) && $role->users_count == 0)
                                            <button type="button"
                                                    class="btn btn-outline-danger"
                                                    title="Delete Role"
                                                    onclick="deleteRole({{ $role->id }}, '{{ $role->name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            @endif
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <small class="text-muted">
                                Showing {{ $roles->firstItem() }} to {{ $roles->lastItem() }} of {{ $roles->total() }} roles
                            </small>
                        </div>
                        <div>
                            {{ $roles->links() }}
                        </div>
                    </div>
                @else
                    <!-- Empty State -->
                    <div class="text-center py-5">
                        <i class="fas fa-user-shield fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">No Roles Found</h4>
                        @if(request('search'))
                            <p class="text-muted">No roles match your search criteria.</p>
                            <a href="{{ route('roles.index') }}" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left me-2"></i>View All Roles
                            </a>
                        @else
                            <p class="text-muted">Get started by creating your first role.</p>
                            @can('roles.create')
                            <a href="{{ route('roles.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create First Role
                            </a>
                            @endcan
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function deleteRole(roleId, roleName) {
    if (confirm(`Are you sure you want to delete the role "${roleName}"? This action cannot be undone.`)) {
        fetch(`/roles/${roleId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showAlert('success', data.message);
                // Reload page after short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'An error occurred while deleting the role.');
        });
    }
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Insert alert at the top of the card body
    const cardBody = document.querySelector('.card-body');
    cardBody.insertAdjacentHTML('afterbegin', alertHtml);
}
</script>
@endpush
