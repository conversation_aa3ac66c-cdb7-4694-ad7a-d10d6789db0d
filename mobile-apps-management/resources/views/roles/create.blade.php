@extends('layouts.app')

@section('title', 'Create Role')
@section('page-title', 'Create New Role')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2 text-primary"></i>
                        Create New Role
                    </h5>
                    <a href="{{ route('roles.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Roles
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('roles.store') }}" id="createRoleForm">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Role Name -->
                            <div class="mb-4">
                                <label for="name" class="form-label">
                                    <i class="fas fa-tag me-2"></i>Role Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control @error('name') is-invalid @enderror" 
                                       id="name" 
                                       name="name" 
                                       value="{{ old('name') }}" 
                                       placeholder="Enter role name"
                                       required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">
                                    Choose a descriptive name for this role (e.g., "Content Manager", "QA Tester")
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Role Summary -->
                            <div class="card bg-light border-0">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-info-circle me-2"></i>Role Summary
                                    </h6>
                                    <div id="roleSummary">
                                        <p class="text-muted mb-2">Selected Permissions: <span id="permissionCount">0</span></p>
                                        <div id="selectedModules" class="d-flex flex-wrap gap-1"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Permissions Section -->
                    <div class="mb-4">
                        <h6 class="mb-3">
                            <i class="fas fa-shield-alt me-2"></i>Permissions
                        </h6>
                        <div class="row">
                            @foreach($permissions as $module => $modulePermissions)
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card border">
                                    <div class="card-header bg-light">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">
                                                <i class="fas fa-{{ $module === 'users' ? 'users' : ($module === 'roles' ? 'user-shield' : ($module === 'apps' ? 'mobile-alt' : ($module === 'sprints' ? 'calendar-alt' : 'tachometer-alt'))) }} me-2"></i>
                                                {{ ucfirst($module) }}
                                            </h6>
                                            <div class="form-check">
                                                <input class="form-check-input module-toggle" 
                                                       type="checkbox" 
                                                       id="toggle_{{ $module }}"
                                                       data-module="{{ $module }}">
                                                <label class="form-check-label" for="toggle_{{ $module }}">
                                                    All
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        @foreach($modulePermissions as $permission)
                                        <div class="form-check mb-2">
                                            <input class="form-check-input permission-checkbox" 
                                                   type="checkbox" 
                                                   name="permissions[]" 
                                                   value="{{ $permission->name }}" 
                                                   id="permission_{{ $permission->id }}"
                                                   data-module="{{ $module }}"
                                                   {{ in_array($permission->name, old('permissions', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="permission_{{ $permission->id }}">
                                                <span class="badge bg-{{ explode('.', $permission->name)[1] === 'create' ? 'success' : (explode('.', $permission->name)[1] === 'read' ? 'info' : (explode('.', $permission->name)[1] === 'update' ? 'warning' : 'danger')) }}">
                                                    {{ strtoupper(explode('.', $permission->name)[1]) }}
                                                </span>
                                                {{ ucfirst(explode('.', $permission->name)[1]) }} {{ ucfirst($module) }}
                                            </label>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Quick Permission Templates -->
                    <div class="mb-4">
                        <h6 class="mb-3">
                            <i class="fas fa-magic me-2"></i>Quick Templates
                        </h6>
                        <div class="btn-group flex-wrap" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="applyTemplate('viewer')">
                                <i class="fas fa-eye me-2"></i>Viewer (Read Only)
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="applyTemplate('editor')">
                                <i class="fas fa-edit me-2"></i>Editor (Read + Write)
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="applyTemplate('manager')">
                                <i class="fas fa-user-tie me-2"></i>Manager (Most Permissions)
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearAllPermissions()">
                                <i class="fas fa-times me-2"></i>Clear All
                            </button>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('roles.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save me-2"></i>
                            <span class="btn-text">Create Role</span>
                            <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createRoleForm');
    const submitBtn = document.getElementById('submitBtn');
    const permissionCheckboxes = document.querySelectorAll('.permission-checkbox');
    const moduleToggles = document.querySelectorAll('.module-toggle');

    // Update role summary when permissions change
    function updateRoleSummary() {
        const checkedPermissions = document.querySelectorAll('.permission-checkbox:checked');
        const permissionCount = document.getElementById('permissionCount');
        const selectedModules = document.getElementById('selectedModules');
        
        permissionCount.textContent = checkedPermissions.length;
        
        // Group by modules
        const modules = {};
        checkedPermissions.forEach(checkbox => {
            const module = checkbox.dataset.module;
            if (!modules[module]) modules[module] = 0;
            modules[module]++;
        });
        
        selectedModules.innerHTML = '';
        Object.keys(modules).forEach(module => {
            const badge = document.createElement('span');
            badge.className = 'badge bg-primary';
            badge.textContent = `${module} (${modules[module]})`;
            selectedModules.appendChild(badge);
        });
    }

    // Module toggle functionality
    moduleToggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const module = this.dataset.module;
            const moduleCheckboxes = document.querySelectorAll(`[data-module="${module}"]`);
            
            moduleCheckboxes.forEach(checkbox => {
                if (checkbox !== this) {
                    checkbox.checked = this.checked;
                }
            });
            
            updateRoleSummary();
        });
    });

    // Individual permission checkbox change
    permissionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const module = this.dataset.module;
            const moduleCheckboxes = document.querySelectorAll(`[data-module="${module}"].permission-checkbox`);
            const moduleToggle = document.querySelector(`[data-module="${module}"].module-toggle`);
            
            // Update module toggle state
            const checkedCount = document.querySelectorAll(`[data-module="${module}"].permission-checkbox:checked`).length;
            moduleToggle.checked = checkedCount === moduleCheckboxes.length;
            moduleToggle.indeterminate = checkedCount > 0 && checkedCount < moduleCheckboxes.length;
            
            updateRoleSummary();
        });
    });

    // Form submission
    form.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.querySelector('.btn-text').textContent = 'Creating...';
        submitBtn.querySelector('.spinner-border').classList.remove('d-none');
    });

    // Initialize summary
    updateRoleSummary();
});

// Permission templates
function applyTemplate(template) {
    const checkboxes = document.querySelectorAll('.permission-checkbox');
    
    // Clear all first
    checkboxes.forEach(cb => cb.checked = false);
    
    switch(template) {
        case 'viewer':
            checkboxes.forEach(cb => {
                if (cb.value.includes('.read')) {
                    cb.checked = true;
                }
            });
            break;
        case 'editor':
            checkboxes.forEach(cb => {
                if (cb.value.includes('.read') || cb.value.includes('.create') || cb.value.includes('.update')) {
                    cb.checked = true;
                }
            });
            break;
        case 'manager':
            checkboxes.forEach(cb => {
                if (!cb.value.includes('system.admin')) {
                    cb.checked = true;
                }
            });
            break;
    }
    
    // Update module toggles and summary
    document.querySelectorAll('.module-toggle').forEach(toggle => {
        const module = toggle.dataset.module;
        const moduleCheckboxes = document.querySelectorAll(`[data-module="${module}"].permission-checkbox`);
        const checkedCount = document.querySelectorAll(`[data-module="${module}"].permission-checkbox:checked`).length;
        
        toggle.checked = checkedCount === moduleCheckboxes.length;
        toggle.indeterminate = checkedCount > 0 && checkedCount < moduleCheckboxes.length;
    });
    
    updateRoleSummary();
}

function clearAllPermissions() {
    document.querySelectorAll('.permission-checkbox').forEach(cb => cb.checked = false);
    document.querySelectorAll('.module-toggle').forEach(toggle => {
        toggle.checked = false;
        toggle.indeterminate = false;
    });
    updateRoleSummary();
}

function updateRoleSummary() {
    const checkedPermissions = document.querySelectorAll('.permission-checkbox:checked');
    const permissionCount = document.getElementById('permissionCount');
    const selectedModules = document.getElementById('selectedModules');
    
    permissionCount.textContent = checkedPermissions.length;
    
    // Group by modules
    const modules = {};
    checkedPermissions.forEach(checkbox => {
        const module = checkbox.dataset.module;
        if (!modules[module]) modules[module] = 0;
        modules[module]++;
    });
    
    selectedModules.innerHTML = '';
    Object.keys(modules).forEach(module => {
        const badge = document.createElement('span');
        badge.className = 'badge bg-primary me-1 mb-1';
        badge.textContent = `${module} (${modules[module]})`;
        selectedModules.appendChild(badge);
    });
}
</script>
@endpush
