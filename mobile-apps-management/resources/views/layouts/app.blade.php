<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Mobile Apps Management') }} - @yield('title', 'Dashboard')</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom Styles -->
    <style>
        /* Lighter placeholder text for all inputs */
        ::placeholder {
            color: #adb5bd !important;
            opacity: 0.7 !important;
        }

        /* Ensure consistent placeholder styling across browsers */
        ::-webkit-input-placeholder { /* Chrome/Opera/Safari */
            color: #adb5bd !important;
            opacity: 0.7 !important;
        }
        ::-moz-placeholder { /* Firefox 19+ */
            color: #adb5bd !important;
            opacity: 0.7 !important;
        }
        :-ms-input-placeholder { /* IE 10+ */
            color: #adb5bd !important;
            opacity: 0.7 !important;
        }
        :-moz-placeholder { /* Firefox 18- */
            color: #adb5bd !important;
            opacity: 0.7 !important;
        }

        /* Form control focus states */
        .form-control:focus {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        /* Custom app management styles */
        .app-card {
            transition: transform 0.2s ease-in-out;
        }

        .app-card:hover {
            transform: translateY(-2px);
        }

        .platform-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .status-active { background-color: #198754; }
        .status-quarantined { background-color: #ffc107; }
        .status-inactive { background-color: #6c757d; }
    </style>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 0.5rem;
            margin: 0.2rem 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .navbar-brand {
            font-weight: 600;
        }
        .alert {
            border: none;
            border-radius: 0.5rem;
        }
        .btn {
            border-radius: 0.5rem;
        }
        .form-control, .form-select {
            border-radius: 0.5rem;
        }
        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .badge {
            font-size: 0.75em;
        }
        .cursor-pointer {
            cursor: pointer;
        }
        .cursor-pointer:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
    </style>

    @stack('styles')
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-mobile-alt me-2"></i>
                            Mobile Apps
                        </h4>
                        <small class="text-white-50">Management Portal</small>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}"
                               href="{{ route('dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>

                        @can('users.read')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('users.*') ? 'active' : '' }}"
                               href="{{ route('users.index') }}">
                                <i class="fas fa-users me-2"></i>
                                User Management
                            </a>
                        </li>
                        @endcan

                        @can('roles.read')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('roles.*') ? 'active' : '' }}"
                               href="{{ route('roles.index') }}">
                                <i class="fas fa-user-shield me-2"></i>
                                Roles & Permissions
                            </a>
                        </li>
                        @endcan

                        @can('apps.read')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('apps.*') ? 'active' : '' }}"
                               href="{{ route('apps.index') }}">
                                <i class="fas fa-mobile-alt me-2"></i>
                                Apps Management
                            </a>
                        </li>
                        @endcan

                        @can('dashboard.read')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('files.*') ? 'active' : '' }}"
                               href="{{ route('files.storage-management') }}">
                                <i class="fas fa-hdd me-2"></i>
                                File Storage
                            </a>
                        </li>
                        @endcan

                        @can('sprints.read')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('sprints.*') ? 'active' : '' }}"
                               href="{{ route('sprints.index') }}">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Sprint Management
                            </a>
                        </li>
                        @endcan

                        @can('audit_logs.view')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('audit-logs.*') ? 'active' : '' }}"
                               href="{{ route('audit-logs.index') }}">
                                <i class="fas fa-clipboard-list me-2"></i>
                                Audit Logs
                            </a>
                        </li>
                        @endcan
                    </ul>

                    <hr class="text-white-50 my-4">

                    <div class="text-center">
                        <small class="text-white-50">
                            Logged in as:<br>
                            <strong class="text-white">{{ auth()->user()->name }}</strong><br>
                            <span class="badge bg-light text-dark">{{ auth()->user()->getRoleNames()->first() }}</span>
                        </small>
                    </div>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Top Navigation -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">@yield('page-title', 'Dashboard')</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                {{ auth()->user()->name }}
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}" class="d-inline">
                                        @csrf
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Flash Messages -->
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('warning'))
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {{ session('warning') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('info'))
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ session('info') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <!-- Page Content -->
                @yield('content')
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    @stack('scripts')
</body>
</html>
