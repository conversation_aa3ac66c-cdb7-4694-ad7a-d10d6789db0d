<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Mobile Apps Management') }} - @yield('title', 'Login')</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .auth-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
            border: none;
            overflow: hidden;
        }
        .auth-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .auth-body {
            padding: 2rem;
        }
        .form-control {
            border-radius: 0.5rem;
            border: 1px solid #e0e6ed;
            padding: 0.75rem 1rem;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .alert {
            border: none;
            border-radius: 0.5rem;
        }
        .input-group-text {
            background-color: #f8f9fa;
            border: 1px solid #e0e6ed;
            border-radius: 0.5rem 0 0 0.5rem;
        }
        .form-floating > .form-control {
            border-radius: 0.5rem;
        }
        .auth-footer {
            background-color: #f8f9fa;
            padding: 1rem 2rem;
            text-align: center;
            border-top: 1px solid #e0e6ed;
        }
        .logo-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .security-notice {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1rem;
            margin-top: 1rem;
            border-radius: 0 0.5rem 0.5rem 0;
        }
    </style>

    @stack('styles')
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card auth-card">
                    <div class="auth-header">
                        <div class="logo-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3 class="mb-0">Mobile Apps Management</h3>
                        <p class="mb-0 opacity-75">Enterprise Portal</p>
                    </div>

                    <div class="auth-body">
                        <!-- Flash Messages -->
                        @if(session('success'))
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                {{ session('success') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                {{ session('error') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        @endif

                        @if(session('warning'))
                            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                {{ session('warning') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        @endif

                        @if(session('info'))
                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                <i class="fas fa-info-circle me-2"></i>
                                {{ session('info') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        @endif

                        @yield('content')
                    </div>

                    @hasSection('footer')
                    <div class="auth-footer">
                        @yield('footer')
                    </div>
                    @endif
                </div>

                <!-- Security Notice -->
                <div class="security-notice mt-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-shield-alt text-primary me-2"></i>
                        <small class="text-muted">
                            <strong>Security Notice:</strong> This is a secure enterprise portal. 
                            All access attempts are logged and monitored.
                        </small>
                    </div>
                </div>

                <!-- Default Credentials Notice (only in development) -->
                @if(app()->environment('local'))
                <div class="alert alert-info mt-3">
                    <h6><i class="fas fa-info-circle me-2"></i>Development Mode - Default Credentials</h6>
                    <small>
                        <strong>Super Admin:</strong> <EMAIL> / password123<br>
                        <strong>Admin:</strong> <EMAIL> / password123<br>
                        <strong>Developer:</strong> <EMAIL> / password123
                    </small>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    @stack('scripts')
</body>
</html>
