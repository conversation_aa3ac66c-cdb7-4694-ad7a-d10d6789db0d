@extends('layouts.auth')

@section('title', 'Login')

@section('content')
<form method="POST" action="{{ route('login') }}" id="loginForm">
    @csrf
    
    <div class="mb-4">
        <h4 class="text-center mb-3">Sign In</h4>
        <p class="text-muted text-center">Enter your credentials to access the portal</p>
    </div>

    <!-- Email Field -->
    <div class="form-floating mb-3">
        <input type="email" 
               class="form-control @error('email') is-invalid @enderror" 
               id="email" 
               name="email" 
               placeholder="<EMAIL>"
               value="{{ old('email') }}" 
               required 
               autofocus>
        <label for="email">
            <i class="fas fa-envelope me-2"></i>Email Address
        </label>
        @error('email')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
        @enderror
    </div>

    <!-- Password Field -->
    <div class="form-floating mb-3">
        <input type="password" 
               class="form-control @error('password') is-invalid @enderror" 
               id="password" 
               name="password" 
               placeholder="Password"
               required>
        <label for="password">
            <i class="fas fa-lock me-2"></i>Password
        </label>
        @error('password')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
        @enderror
    </div>

    <!-- Remember Me -->
    <div class="form-check mb-4">
        <input class="form-check-input" type="checkbox" id="remember" name="remember" {{ old('remember') ? 'checked' : '' }}>
        <label class="form-check-label" for="remember">
            Remember me on this device
        </label>
    </div>

    <!-- Submit Button -->
    <div class="d-grid">
        <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
            <i class="fas fa-sign-in-alt me-2"></i>
            <span class="btn-text">Sign In</span>
            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
        </button>
    </div>
</form>

<!-- Security Features Info -->
<div class="mt-4 p-3 bg-light rounded">
    <h6 class="text-muted mb-2">
        <i class="fas fa-shield-alt me-2"></i>Security Features
    </h6>
    <ul class="list-unstyled mb-0 small text-muted">
        <li><i class="fas fa-check text-success me-2"></i>Enterprise-grade authentication</li>
        <li><i class="fas fa-check text-success me-2"></i>Account lockout protection</li>
        <li><i class="fas fa-check text-success me-2"></i>Comprehensive audit logging</li>
        <li><i class="fas fa-check text-success me-2"></i>Session security monitoring</li>
    </ul>
</div>
@endsection

@section('footer')
<small class="text-muted">
    <i class="fas fa-clock me-1"></i>
    Session timeout: {{ config('security.authentication.session_timeout', 120) }} minutes
</small>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const btnText = loginBtn.querySelector('.btn-text');
    const spinner = loginBtn.querySelector('.spinner-border');

    loginForm.addEventListener('submit', function() {
        // Disable button and show loading state
        loginBtn.disabled = true;
        btnText.textContent = 'Signing In...';
        spinner.classList.remove('d-none');
    });

    // Auto-focus on email field if empty
    const emailField = document.getElementById('email');
    if (!emailField.value) {
        emailField.focus();
    }

    // Password visibility toggle (optional enhancement)
    const passwordField = document.getElementById('password');
    const togglePassword = document.createElement('button');
    togglePassword.type = 'button';
    togglePassword.className = 'btn btn-outline-secondary position-absolute end-0 top-50 translate-middle-y me-2';
    togglePassword.style.border = 'none';
    togglePassword.style.background = 'none';
    togglePassword.innerHTML = '<i class="fas fa-eye"></i>';
    
    // Add toggle functionality
    togglePassword.addEventListener('click', function() {
        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordField.setAttribute('type', type);
        this.innerHTML = type === 'password' ? '<i class="fas fa-eye"></i>' : '<i class="fas fa-eye-slash"></i>';
    });

    // Insert toggle button
    const passwordContainer = passwordField.parentElement;
    passwordContainer.style.position = 'relative';
    passwordContainer.appendChild(togglePassword);

    // Form validation enhancement
    const inputs = loginForm.querySelectorAll('input[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value.trim() === '') {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
            }
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid') && this.value.trim() !== '') {
                this.classList.remove('is-invalid');
            }
        });
    });

    // Email validation
    emailField.addEventListener('blur', function() {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (this.value && !emailRegex.test(this.value)) {
            this.classList.add('is-invalid');
        }
    });

    // Caps Lock detection
    document.addEventListener('keydown', function(e) {
        if (e.getModifierState && e.getModifierState('CapsLock')) {
            showCapsLockWarning();
        }
    });

    function showCapsLockWarning() {
        let warning = document.getElementById('capsLockWarning');
        if (!warning) {
            warning = document.createElement('div');
            warning.id = 'capsLockWarning';
            warning.className = 'alert alert-warning alert-sm mt-2';
            warning.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Caps Lock is on';
            passwordField.parentElement.appendChild(warning);
        }
    }
});
</script>
@endpush
