@extends('layouts.auth')

@section('title', 'Change Password')

@section('content')
<form method="POST" action="{{ route('password.change') }}" id="changePasswordForm">
    @csrf
    
    <div class="mb-4">
        <h4 class="text-center mb-3">
            <i class="fas fa-key text-warning me-2"></i>
            Password Change Required
        </h4>
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Your password has expired and must be changed before you can continue.
        </div>
    </div>

    <!-- Current Password Field -->
    <div class="form-floating mb-3">
        <input type="password" 
               class="form-control @error('current_password') is-invalid @enderror" 
               id="current_password" 
               name="current_password" 
               placeholder="Current Password"
               required 
               autofocus>
        <label for="current_password">
            <i class="fas fa-lock me-2"></i>Current Password
        </label>
        @error('current_password')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
        @enderror
    </div>

    <!-- New Password Field -->
    <div class="form-floating mb-3">
        <input type="password" 
               class="form-control @error('password') is-invalid @enderror" 
               id="password" 
               name="password" 
               placeholder="New Password"
               required
               minlength="{{ config('security.authentication.password_min_length', 8) }}">
        <label for="password">
            <i class="fas fa-key me-2"></i>New Password
        </label>
        @error('password')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
        @enderror
        <div class="form-text">
            Password must be at least {{ config('security.authentication.password_min_length', 8) }} characters long.
        </div>
    </div>

    <!-- Confirm Password Field -->
    <div class="form-floating mb-4">
        <input type="password" 
               class="form-control @error('password_confirmation') is-invalid @enderror" 
               id="password_confirmation" 
               name="password_confirmation" 
               placeholder="Confirm New Password"
               required>
        <label for="password_confirmation">
            <i class="fas fa-check-double me-2"></i>Confirm New Password
        </label>
        @error('password_confirmation')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
        @enderror
    </div>

    <!-- Password Strength Indicator -->
    <div class="mb-3">
        <div class="d-flex justify-content-between align-items-center mb-1">
            <small class="text-muted">Password Strength:</small>
            <small id="strengthText" class="text-muted">Enter password</small>
        </div>
        <div class="progress" style="height: 4px;">
            <div id="strengthBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
        </div>
    </div>

    <!-- Password Requirements -->
    <div class="mb-4">
        <small class="text-muted d-block mb-2">Password Requirements:</small>
        <ul class="list-unstyled small">
            <li id="req-length" class="text-muted">
                <i class="fas fa-circle me-2"></i>
                At least {{ config('security.authentication.password_min_length', 8) }} characters
            </li>
            @if(config('security.authentication.require_password_complexity', true))
            <li id="req-uppercase" class="text-muted">
                <i class="fas fa-circle me-2"></i>
                One uppercase letter
            </li>
            <li id="req-lowercase" class="text-muted">
                <i class="fas fa-circle me-2"></i>
                One lowercase letter
            </li>
            <li id="req-number" class="text-muted">
                <i class="fas fa-circle me-2"></i>
                One number
            </li>
            <li id="req-special" class="text-muted">
                <i class="fas fa-circle me-2"></i>
                One special character
            </li>
            @endif
        </ul>
    </div>

    <!-- Submit Button -->
    <div class="d-grid">
        <button type="submit" class="btn btn-primary btn-lg" id="changePasswordBtn" disabled>
            <i class="fas fa-save me-2"></i>
            <span class="btn-text">Change Password</span>
            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
        </button>
    </div>
</form>
@endsection

@section('footer')
<small class="text-muted">
    <i class="fas fa-info-circle me-1"></i>
    For security, you'll be automatically logged in after changing your password.
</small>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('changePasswordForm');
    const passwordField = document.getElementById('password');
    const confirmField = document.getElementById('password_confirmation');
    const submitBtn = document.getElementById('changePasswordBtn');
    const strengthBar = document.getElementById('strengthBar');
    const strengthText = document.getElementById('strengthText');

    // Password strength checking
    passwordField.addEventListener('input', function() {
        const password = this.value;
        const strength = calculatePasswordStrength(password);
        updatePasswordStrength(strength);
        checkPasswordRequirements(password);
        validateForm();
    });

    confirmField.addEventListener('input', validateForm);

    function calculatePasswordStrength(password) {
        let score = 0;
        
        if (password.length >= 8) score += 20;
        if (password.length >= 12) score += 10;
        if (/[a-z]/.test(password)) score += 20;
        if (/[A-Z]/.test(password)) score += 20;
        if (/[0-9]/.test(password)) score += 20;
        if (/[^A-Za-z0-9]/.test(password)) score += 10;
        
        return Math.min(score, 100);
    }

    function updatePasswordStrength(strength) {
        strengthBar.style.width = strength + '%';
        
        if (strength < 30) {
            strengthBar.className = 'progress-bar bg-danger';
            strengthText.textContent = 'Weak';
            strengthText.className = 'text-danger';
        } else if (strength < 60) {
            strengthBar.className = 'progress-bar bg-warning';
            strengthText.textContent = 'Fair';
            strengthText.className = 'text-warning';
        } else if (strength < 80) {
            strengthBar.className = 'progress-bar bg-info';
            strengthText.textContent = 'Good';
            strengthText.className = 'text-info';
        } else {
            strengthBar.className = 'progress-bar bg-success';
            strengthText.textContent = 'Strong';
            strengthText.className = 'text-success';
        }
    }

    function checkPasswordRequirements(password) {
        const requirements = {
            'req-length': password.length >= {{ config('security.authentication.password_min_length', 8) }},
            @if(config('security.authentication.require_password_complexity', true))
            'req-uppercase': /[A-Z]/.test(password),
            'req-lowercase': /[a-z]/.test(password),
            'req-number': /[0-9]/.test(password),
            'req-special': /[^A-Za-z0-9]/.test(password),
            @endif
        };

        Object.keys(requirements).forEach(reqId => {
            const element = document.getElementById(reqId);
            if (requirements[reqId]) {
                element.className = 'text-success';
                element.querySelector('i').className = 'fas fa-check-circle me-2';
            } else {
                element.className = 'text-muted';
                element.querySelector('i').className = 'fas fa-circle me-2';
            }
        });
    }

    function validateForm() {
        const password = passwordField.value;
        const confirmPassword = confirmField.value;
        const currentPassword = document.getElementById('current_password').value;
        
        // Check if all fields are filled
        if (!currentPassword || !password || !confirmPassword) {
            submitBtn.disabled = true;
            return;
        }

        // Check password length
        if (password.length < {{ config('security.authentication.password_min_length', 8) }}) {
            submitBtn.disabled = true;
            return;
        }

        // Check password confirmation
        if (password !== confirmPassword) {
            submitBtn.disabled = true;
            confirmField.classList.add('is-invalid');
            return;
        } else {
            confirmField.classList.remove('is-invalid');
        }

        @if(config('security.authentication.require_password_complexity', true))
        // Check password complexity
        const hasUpper = /[A-Z]/.test(password);
        const hasLower = /[a-z]/.test(password);
        const hasNumber = /[0-9]/.test(password);
        const hasSpecial = /[^A-Za-z0-9]/.test(password);
        
        if (!hasUpper || !hasLower || !hasNumber || !hasSpecial) {
            submitBtn.disabled = true;
            return;
        }
        @endif

        submitBtn.disabled = false;
    }

    // Form submission
    form.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.querySelector('.btn-text').textContent = 'Changing Password...';
        submitBtn.querySelector('.spinner-border').classList.remove('d-none');
    });

    // Real-time password confirmation validation
    confirmField.addEventListener('input', function() {
        if (this.value && this.value !== passwordField.value) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });
});
</script>
@endpush
