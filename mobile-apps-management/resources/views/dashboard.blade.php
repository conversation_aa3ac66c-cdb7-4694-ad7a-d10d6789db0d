@extends('layouts.app')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('content')
<div class="row">
    <!-- Welcome Card -->
    <div class="col-12 mb-4">
        <div class="card border-0 bg-gradient" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="card-body text-white">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-1">Welcome back, {{ auth()->user()->name }}!</h4>
                        <p class="mb-0 opacity-75">
                            You're logged in as <strong>{{ auth()->user()->getRoleNames()->first() }}</strong>
                        </p>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-circle fa-3x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Statistics Cards -->
    @can('apps.read')
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Apps
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ \App\Models\App::count() }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-mobile-alt fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endcan

    @can('sprints.read')
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Active Sprints
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ \App\Models\Sprint::active()->count() }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-alt fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endcan

    @can('users.read')
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Users
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ \App\Models\User::where('is_active', true)->count() }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endcan

    @can('audit_logs.view')
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Today's Activities
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ \App\Models\AuditLog::whereDate('created_at', today())->count() }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clipboard-list fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endcan
</div>

<div class="row">
    <!-- Recent Apps -->
    @can('apps.read')
    <div class="col-lg-8 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0 font-weight-bold text-primary">
                        <i class="fas fa-mobile-alt me-2"></i>Recent Apps
                    </h6>
                    <a href="{{ route('apps.index') }}" class="btn btn-sm btn-outline-primary">
                        View All <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
            <div class="card-body">
                @php
                    $recentApps = \App\Models\App::with(['sprint', 'uploader'])
                                                ->fromActiveSprintsOnly()
                                                ->orderBy('created_at', 'desc')
                                                ->limit(5)
                                                ->get();
                @endphp

                @if($recentApps->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>App</th>
                                    <th>Platform</th>
                                    <th>Version + Build</th>
                                    <th>Sprint</th>
                                    <th>Uploaded</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentApps as $app)
                                <tr class="cursor-pointer" onclick="window.location.href='{{ route('apps.show', $app) }}'">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($app->icon_path)
                                                <img src="{{ Storage::url($app->icon_path) }}"
                                                     alt="{{ $app->name }}"
                                                     class="rounded me-2"
                                                     style="width: 24px; height: 24px;">
                                            @else
                                                <i class="{{ $app->getPlatformIcon() }} text-{{ $app->getPlatformColor() }} me-2"></i>
                                            @endif
                                            <strong>{{ $app->name }}</strong>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $app->getPlatformColor() }}">
                                            {{ ucfirst($app->platform) }}
                                        </span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $app->version }}</strong>
                                            @if($app->build_number)
                                                <small class="text-muted">({{ $app->build_number }})</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        @if($app->sprint)
                                            <span class="badge bg-info">{{ $app->sprint->name }}</span>
                                        @else
                                            <span class="text-muted">No Sprint</span>
                                        @endif
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ $app->created_at->diffForHumans() }}
                                        </small>
                                    </td>
                                    <td>
                                        <a href="{{ route('apps.show', $app) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-mobile-alt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No apps uploaded yet</p>
                        @can('apps.create')
                        <a href="{{ route('apps.index') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Upload First App
                        </a>
                        @endcan
                    </div>
                @endif
            </div>
        </div>
    </div>
    @endcan

    <!-- Quick Actions & System Status -->
    <div class="col-lg-4 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h6 class="mb-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    @can('apps.create')
                    <a href="{{ route('apps.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-upload me-2"></i>Upload New App
                    </a>
                    @endcan

                    @can('sprints.create')
                    <a href="{{ route('sprints.index') }}" class="btn btn-outline-success">
                        <i class="fas fa-plus me-2"></i>Create Sprint
                    </a>
                    @endcan

                    @can('users.create')
                    <a href="{{ route('users.index') }}" class="btn btn-outline-info">
                        <i class="fas fa-user-plus me-2"></i>Add User
                    </a>
                    @endcan

                    @can('audit_logs.view')
                    <a href="{{ route('audit-logs.index') }}" class="btn btn-outline-warning">
                        <i class="fas fa-clipboard-list me-2"></i>View Audit Logs
                    </a>
                    @endcan
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-white border-0">
                <h6 class="mb-0 font-weight-bold text-primary">
                    <i class="fas fa-server me-2"></i>System Status
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <div class="h6 mb-0 text-success">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <small class="text-muted">System</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="h6 mb-0 text-success">
                            <i class="fas fa-database"></i>
                        </div>
                        <small class="text-muted">Database</small>
                    </div>
                </div>
                <hr>
                <div class="small text-muted">
                    <div class="d-flex justify-content-between">
                        <span>Laravel Version:</span>
                        <span>{{ app()->version() }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>PHP Version:</span>
                        <span>{{ PHP_VERSION }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Environment:</span>
                        <span class="badge bg-{{ app()->environment('production') ? 'success' : 'warning' }}">
                            {{ ucfirst(app()->environment()) }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
