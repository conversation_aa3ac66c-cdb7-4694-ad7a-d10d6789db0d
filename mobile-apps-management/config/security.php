<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Enterprise Security Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains security settings for the Mobile Apps Management
    | portal with enterprise-level security features.
    |
    */

    'file_access' => [
        // Token expiry time in seconds (1 hour default)
        'token_expiry' => env('FILE_ACCESS_TOKEN_EXPIRY', 3600),
        
        // Maximum downloads per user per hour
        'max_downloads_per_hour' => env('MAX_DOWNLOADS_PER_HOUR', 50),
        
        // Require IP whitelisting for file access
        'require_ip_whitelist' => env('REQUIRE_IP_WHITELIST', false),
        
        // IP whitelist (comma-separated)
        'ip_whitelist' => env('IP_WHITELIST', ''),
        
        // Log all file access attempts
        'log_all_access' => env('LOG_ALL_ACCESS', true),
        
        // Enable rate limiting on file downloads
        'enable_rate_limiting' => env('ENABLE_RATE_LIMITING', true),
        
        // Block suspicious IPs automatically
        'auto_block_suspicious_ips' => env('AUTO_BLOCK_SUSPICIOUS_IPS', true),
        
        // Maximum failed attempts before blocking IP
        'max_failed_attempts' => env('MAX_FAILED_ATTEMPTS', 10),
        
        // IP block duration in minutes
        'ip_block_duration' => env('IP_BLOCK_DURATION', 60),
    ],

    'upload_security' => [
        // Quarantine new uploads for security scanning
        'quarantine_duration' => env('QUARANTINE_DURATION', 300), // 5 minutes
        
        // Maximum file size for uploads
        'max_file_size' => env('MAX_FILE_SIZE', '500MB'),
        
        // Allowed file extensions
        'allowed_extensions' => ['ipa', 'apk'],
        
        // Enable virus scanning (requires ClamAV)
        'virus_scan_enabled' => env('VIRUS_SCAN_ENABLED', false),
        
        // Virus scan timeout in seconds
        'virus_scan_timeout' => env('VIRUS_SCAN_TIMEOUT', 60),
        
        // Enable file content validation
        'validate_file_content' => env('VALIDATE_FILE_CONTENT', true),
        
        // Enable metadata sanitization
        'sanitize_metadata' => env('SANITIZE_METADATA', true),
        
        // Store original filename
        'store_original_filename' => env('STORE_ORIGINAL_FILENAME', true),
    ],

    'authentication' => [
        // Session timeout in minutes
        'session_timeout' => env('SESSION_TIMEOUT', 120), // 2 hours
        
        // Maximum concurrent sessions per user
        'max_concurrent_sessions' => env('MAX_CONCURRENT_SESSIONS', 3),
        
        // Enable two-factor authentication
        'enable_2fa' => env('ENABLE_2FA', false),
        
        // Password minimum length
        'password_min_length' => env('PASSWORD_MIN_LENGTH', 8),
        
        // Require password complexity
        'require_password_complexity' => env('REQUIRE_PASSWORD_COMPLEXITY', true),
        
        // Account lockout after failed attempts
        'enable_account_lockout' => env('ENABLE_ACCOUNT_LOCKOUT', true),
        
        // Maximum login attempts before lockout
        'max_login_attempts' => env('MAX_LOGIN_ATTEMPTS', 5),
        
        // Account lockout duration in minutes
        'account_lockout_duration' => env('ACCOUNT_LOCKOUT_DURATION', 30),
        
        // Force password change after days
        'force_password_change_days' => env('FORCE_PASSWORD_CHANGE_DAYS', 90),
    ],

    'audit_logging' => [
        // Enable comprehensive audit logging
        'enabled' => env('AUDIT_LOGGING_ENABLED', true),
        
        // Log retention period in days
        'retention_days' => env('AUDIT_LOG_RETENTION_DAYS', 365),
        
        // Log levels to capture
        'log_levels' => ['low', 'medium', 'high', 'critical'],
        
        // Events to always log
        'always_log_events' => [
            'login',
            'logout',
            'failed_login',
            'password_change',
            'user_created',
            'user_deleted',
            'role_assigned',
            'permission_granted',
            'file_upload',
            'file_download',
            'file_delete',
            'sprint_created',
            'sprint_ended',
            'cleanup_performed',
        ],
        
        // Enable real-time alerts for critical events
        'enable_real_time_alerts' => env('ENABLE_REAL_TIME_ALERTS', true),
        
        // Alert email addresses (comma-separated)
        'alert_emails' => env('ALERT_EMAILS', ''),
        
        // Enable database logging
        'log_to_database' => env('LOG_TO_DATABASE', true),
        
        // Enable file logging
        'log_to_file' => env('LOG_TO_FILE', true),
    ],

    'data_protection' => [
        // Enable database encryption for sensitive data
        'encrypt_sensitive_data' => env('ENCRYPT_SENSITIVE_DATA', true),
        
        // Enable secure headers
        'enable_secure_headers' => env('ENABLE_SECURE_HEADERS', true),
        
        // Content Security Policy
        'csp_enabled' => env('CSP_ENABLED', true),
        
        // HSTS max age in seconds
        'hsts_max_age' => env('HSTS_MAX_AGE', 31536000), // 1 year
        
        // Enable CSRF protection
        'csrf_protection' => env('CSRF_PROTECTION', true),
        
        // Enable XSS protection
        'xss_protection' => env('XSS_PROTECTION', true),
        
        // Enable clickjacking protection
        'clickjacking_protection' => env('CLICKJACKING_PROTECTION', true),
    ],

    'monitoring' => [
        // Enable performance monitoring
        'performance_monitoring' => env('PERFORMANCE_MONITORING', true),
        
        // Enable security monitoring
        'security_monitoring' => env('SECURITY_MONITORING', true),
        
        // Monitor storage usage
        'monitor_storage_usage' => env('MONITOR_STORAGE_USAGE', true),
        
        // Storage usage alert threshold (percentage)
        'storage_alert_threshold' => env('STORAGE_ALERT_THRESHOLD', 80),
        
        // Monitor failed login attempts
        'monitor_failed_logins' => env('MONITOR_FAILED_LOGINS', true),
        
        // Monitor suspicious file access patterns
        'monitor_suspicious_access' => env('MONITOR_SUSPICIOUS_ACCESS', true),
    ],
];
