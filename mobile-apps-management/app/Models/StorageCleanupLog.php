<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class StorageCleanupLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'sprint_id',
        'sprint_name',
        'cleaned_by',
        'files_deleted_count',
        'storage_freed_mb',
        'cleanup_summary',
        'apps_deleted',
        'cleanup_type',
        'notes',
        'success',
        'error_message',
        'cleanup_date',
    ];

    protected function casts(): array
    {
        return [
            'cleanup_summary' => 'array',
            'apps_deleted' => 'array',
            'success' => 'boolean',
            'cleanup_date' => 'datetime',
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($log) {
            if (empty($log->cleanup_date)) {
                $log->cleanup_date = now();
            }
        });
    }

    /**
     * Relationships
     */
    public function sprint()
    {
        return $this->belongsTo(Sprint::class);
    }

    public function cleaner()
    {
        return $this->belongsTo(User::class, 'cleaned_by');
    }

    /**
     * Scopes
     */
    public function scopeForSprint($query, $sprintId)
    {
        return $query->where('sprint_id', $sprintId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('cleanup_type', $type);
    }

    public function scopeSuccessful($query)
    {
        return $query->where('success', true);
    }

    public function scopeFailed($query)
    {
        return $query->where('success', false);
    }

    public function scopeRecent($query, $days = 30)
    {
        return $query->where('cleanup_date', '>=', now()->subDays($days));
    }

    /**
     * Static methods
     */
    public static function logCleanup(
        Sprint $sprint,
        int $filesDeleted,
        float $storageFreed,
        array $cleanupSummary,
        array $appsDeleted = [],
        string $type = 'manual',
        string $notes = null,
        bool $success = true,
        string $errorMessage = null
    ): self {
        return static::create([
            'sprint_id' => $sprint->id,
            'sprint_name' => $sprint->name,
            'cleaned_by' => auth()->id(),
            'files_deleted_count' => $filesDeleted,
            'storage_freed_mb' => $storageFreed,
            'cleanup_summary' => $cleanupSummary,
            'apps_deleted' => $appsDeleted,
            'cleanup_type' => $type,
            'notes' => $notes,
            'success' => $success,
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Methods
     */
    public function getStorageFreedFormatted(): string
    {
        if ($this->storage_freed_mb < 1024) {
            return number_format($this->storage_freed_mb, 2) . ' MB';
        }

        return number_format($this->storage_freed_mb / 1024, 2) . ' GB';
    }

    public function getCleanupTypeColor(): string
    {
        return match($this->cleanup_type) {
            'manual' => 'primary',
            'scheduled' => 'info',
            'automatic' => 'warning',
            default => 'secondary'
        };
    }

    public function getCleanupTypeIcon(): string
    {
        return match($this->cleanup_type) {
            'manual' => 'fas fa-user',
            'scheduled' => 'fas fa-clock',
            'automatic' => 'fas fa-robot',
            default => 'fas fa-question'
        };
    }

    public function getStatusColor(): string
    {
        return $this->success ? 'success' : 'danger';
    }

    public function getStatusIcon(): string
    {
        return $this->success ? 'fas fa-check-circle' : 'fas fa-times-circle';
    }
}
