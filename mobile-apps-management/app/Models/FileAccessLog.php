<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class FileAccessLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'app_id',
        'user_id',
        'user_email',
        'access_type',
        'ip_address',
        'user_agent',
        'file_path',
        'access_token',
        'success',
        'error_message',
        'download_method',
        'device_type',
        'platform_detected',
        'metadata',
        'access_date',
    ];

    protected function casts(): array
    {
        return [
            'success' => 'boolean',
            'metadata' => 'array',
            'access_date' => 'datetime',
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($log) {
            if (empty($log->access_date)) {
                $log->access_date = now();
            }

            // Auto-detect device type and platform from user agent
            if (empty($log->device_type) || empty($log->platform_detected)) {
                $userAgent = $log->user_agent ?? request()->userAgent();
                $log->device_type = static::detectDeviceType($userAgent);
                $log->platform_detected = static::detectPlatform($userAgent);
            }
        });
    }

    /**
     * Relationships
     */
    public function app()
    {
        return $this->belongsTo(App::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scopes
     */
    public function scopeForApp($query, $appId)
    {
        return $query->where('app_id', $appId);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByAccessType($query, $type)
    {
        return $query->where('access_type', $type);
    }

    public function scopeSuccessful($query)
    {
        return $query->where('success', true);
    }

    public function scopeFailed($query)
    {
        return $query->where('success', false);
    }

    public function scopeRecent($query, $days = 7)
    {
        return $query->where('access_date', '>=', now()->subDays($days));
    }

    /**
     * Static methods
     */
    public static function logAccess(
        App $app,
        string $accessType,
        bool $success = true,
        string $errorMessage = null,
        string $downloadMethod = null,
        array $metadata = null
    ): self {
        $user = auth()->user();

        return static::create([
            'app_id' => $app->id,
            'user_id' => $user?->id,
            'user_email' => $user?->email,
            'access_type' => $accessType,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'file_path' => $app->file_path,
            'access_token' => request()->get('token'),
            'success' => $success,
            'error_message' => $errorMessage,
            'download_method' => $downloadMethod,
            'metadata' => $metadata,
        ]);
    }

    public static function detectDeviceType(string $userAgent): string
    {
        $userAgent = strtolower($userAgent);

        if (str_contains($userAgent, 'mobile') || str_contains($userAgent, 'android')) {
            return 'mobile';
        } elseif (str_contains($userAgent, 'tablet') || str_contains($userAgent, 'ipad')) {
            return 'tablet';
        } else {
            return 'desktop';
        }
    }

    public static function detectPlatform(string $userAgent): string
    {
        $userAgent = strtolower($userAgent);

        if (str_contains($userAgent, 'iphone') || str_contains($userAgent, 'ipad') || str_contains($userAgent, 'ios')) {
            return 'ios';
        } elseif (str_contains($userAgent, 'android')) {
            return 'android';
        } elseif (str_contains($userAgent, 'windows')) {
            return 'windows';
        } elseif (str_contains($userAgent, 'mac')) {
            return 'mac';
        } elseif (str_contains($userAgent, 'linux')) {
            return 'linux';
        } else {
            return 'web';
        }
    }

    /**
     * Methods
     */
    public function getAccessTypeColor(): string
    {
        return match($this->access_type) {
            'download' => 'primary',
            'view' => 'info',
            'qr_scan' => 'warning',
            'api_access' => 'secondary',
            default => 'light'
        };
    }

    public function getAccessTypeIcon(): string
    {
        return match($this->access_type) {
            'download' => 'fas fa-download',
            'view' => 'fas fa-eye',
            'qr_scan' => 'fas fa-qrcode',
            'api_access' => 'fas fa-code',
            default => 'fas fa-question'
        };
    }

    public function getDeviceTypeIcon(): string
    {
        return match($this->device_type) {
            'mobile' => 'fas fa-mobile-alt',
            'tablet' => 'fas fa-tablet-alt',
            'desktop' => 'fas fa-desktop',
            default => 'fas fa-question'
        };
    }
}
