<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'event',
        'auditable_type',
        'auditable_id',
        'user_id',
        'user_email',
        'ip_address',
        'user_agent',
        'old_values',
        'new_values',
        'metadata',
        'url',
        'method',
        'severity',
        'description',
        'success',
        'error_message',
    ];

    protected function casts(): array
    {
        return [
            'old_values' => 'array',
            'new_values' => 'array',
            'metadata' => 'array',
            'success' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function auditable()
    {
        return $this->morphTo();
    }

    /**
     * Scopes
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeForEvent($query, $event)
    {
        return $query->where('event', $event);
    }

    public function scopeBySeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }

    public function scopeSuccessful($query)
    {
        return $query->where('success', true);
    }

    public function scopeFailed($query)
    {
        return $query->where('success', false);
    }

    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Static methods for logging
     */
    public static function logEvent(
        string $event,
        $auditable = null,
        array $oldValues = null,
        array $newValues = null,
        string $severity = 'medium',
        string $description = null,
        array $metadata = null,
        bool $success = true,
        string $errorMessage = null
    ): self {
        $user = auth()->user();

        return static::create([
            'event' => $event,
            'auditable_type' => $auditable ? get_class($auditable) : null,
            'auditable_id' => $auditable?->id,
            'user_id' => $user?->id,
            'user_email' => $user?->email,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'metadata' => $metadata,
            'url' => request()->fullUrl(),
            'method' => request()->method(),
            'severity' => $severity,
            'description' => $description,
            'success' => $success,
            'error_message' => $errorMessage,
        ]);
    }

    public static function logLogin(User $user, bool $success = true, string $errorMessage = null): self
    {
        return static::logEvent(
            event: $success ? 'login' : 'failed_login',
            auditable: $user,
            severity: $success ? 'low' : 'medium',
            description: $success ? 'User logged in successfully' : 'Failed login attempt',
            success: $success,
            errorMessage: $errorMessage
        );
    }

    public static function logLogout(User $user): self
    {
        return static::logEvent(
            event: 'logout',
            auditable: $user,
            severity: 'low',
            description: 'User logged out'
        );
    }

    public static function logFileAccess(App $app, string $accessType, bool $success = true, string $errorMessage = null): self
    {
        return static::logEvent(
            event: 'file_access',
            auditable: $app,
            severity: $success ? 'low' : 'medium',
            description: "File {$accessType} attempt for app: {$app->name}",
            metadata: [
                'access_type' => $accessType,
                'file_path' => $app->file_path,
                'platform' => $app->platform,
            ],
            success: $success,
            errorMessage: $errorMessage
        );
    }

    /**
     * Methods
     */
    public function getSeverityColor(): string
    {
        return match($this->severity) {
            'low' => 'success',
            'medium' => 'warning',
            'high' => 'danger',
            'critical' => 'dark',
            default => 'secondary'
        };
    }

    public function getSeverityIcon(): string
    {
        return match($this->severity) {
            'low' => 'fas fa-info-circle',
            'medium' => 'fas fa-exclamation-triangle',
            'high' => 'fas fa-exclamation-circle',
            'critical' => 'fas fa-skull-crossbones',
            default => 'fas fa-question-circle'
        };
    }
}
