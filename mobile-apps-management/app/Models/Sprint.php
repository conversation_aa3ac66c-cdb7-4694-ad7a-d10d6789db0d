<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Sprint extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'start_date',
        'end_date',
        'status',
        'storage_size_mb',
        'files_count',
        'cleanup_eligible',
        'last_cleanup_check',
        'created_by',
    ];

    protected function casts(): array
    {
        return [
            'start_date' => 'date',
            'end_date' => 'date',
            'last_cleanup_check' => 'datetime',
            'cleanup_eligible' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function apps()
    {
        return $this->hasMany(App::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function cleanupLogs()
    {
        return $this->hasMany(StorageCleanupLog::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeEnded($query)
    {
        return $query->where('status', 'ended');
    }

    public function scopeCleanupEligible($query)
    {
        return $query->where('cleanup_eligible', true);
    }

    /**
     * Methods
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    public function isEnded(): bool
    {
        return $this->status === 'ended';
    }

    public function canBeDeleted(): bool
    {
        return $this->apps()->count() === 0;
    }

    public function markAsEnded(): void
    {
        $this->update([
            'status' => 'ended',
            'cleanup_eligible' => true,
            'last_cleanup_check' => now(),
        ]);
    }

    public function updateStorageStats(): void
    {
        $apps = $this->apps;
        $totalSize = $apps->sum('file_size');
        $filesCount = $apps->count();

        $this->update([
            'storage_size_mb' => round($totalSize / 1024 / 1024, 2),
            'files_count' => $filesCount,
        ]);
    }

    public function getStorageSizeFormatted(): string
    {
        if ($this->storage_size_mb < 1024) {
            return number_format($this->storage_size_mb, 2) . ' MB';
        }

        return number_format($this->storage_size_mb / 1024, 2) . ' GB';
    }

    public function getStatusColor(): string
    {
        return match($this->status) {
            'planning' => 'secondary',
            'active' => 'success',
            'completed' => 'primary',
            'cancelled' => 'danger',
            default => 'secondary'
        };
    }

    public function getStatusIcon(): string
    {
        return match($this->status) {
            'planning' => 'clock',
            'active' => 'play',
            'completed' => 'check',
            'cancelled' => 'times',
            default => 'question'
        };
    }
}
