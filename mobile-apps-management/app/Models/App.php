<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class App extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'bundle_identifier',
        'version',
        'build_number',
        'platform',
        'file_path',
        'file_name',
        'file_hash',
        'file_size',
        'mime_type',
        'icon_path',
        'changelog',
        'ticket_link',
        'qr_code_path',
        'access_token',
        'metadata',
        'download_count',
        'sprint_id',
        'uploaded_by',
        'upload_date',
        'status',
        'quarantine_until',
        'released_by',
        'released_at',
    ];

    protected function casts(): array
    {
        return [
            'metadata' => 'array',
            'upload_date' => 'datetime',
            'quarantine_until' => 'datetime',
            'released_at' => 'datetime',
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($app) {
            if (empty($app->access_token)) {
                $app->access_token = Str::random(64);
            }
            if (empty($app->upload_date)) {
                $app->upload_date = now();
            }
        });

        static::created(function ($app) {
            // Update sprint storage stats when app is created
            $app->sprint->updateStorageStats();
        });

        static::deleted(function ($app) {
            // Update sprint storage stats when app is deleted
            if ($app->sprint) {
                $app->sprint->updateStorageStats();
            }
        });
    }

    /**
     * Relationships
     */
    public function sprint()
    {
        return $this->belongsTo(Sprint::class)->withTrashed();
    }

    public function activeSprint()
    {
        return $this->belongsTo(Sprint::class, 'sprint_id');
    }

    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    public function fileAccessLogs()
    {
        return $this->hasMany(FileAccessLog::class);
    }

    /**
     * Scopes
     */
    public function scopePlatform($query, $platform)
    {
        return $query->where('platform', $platform);
    }

    public function scopeForSprint($query, $sprintId)
    {
        return $query->where('sprint_id', $sprintId);
    }

    public function scopeFromActiveSprintsOnly($query)
    {
        return $query->whereHas('activeSprint');
    }

    public function scopeIncludeDeletedSprints($query)
    {
        return $query->with(['sprint' => function($q) {
            $q->withTrashed();
        }]);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('bundle_identifier', 'like', "%{$search}%")
              ->orWhere('version', 'like', "%{$search}%");
        });
    }

    /**
     * Methods
     */
    public function getFileSizeFormatted(): string
    {
        $bytes = $this->file_size;

        if ($bytes < 1024) {
            return $bytes . ' B';
        } elseif ($bytes < 1048576) {
            return round($bytes / 1024, 2) . ' KB';
        } elseif ($bytes < 1073741824) {
            return round($bytes / 1048576, 2) . ' MB';
        } else {
            return round($bytes / 1073741824, 2) . ' GB';
        }
    }

    public function getPlatformIcon(): string
    {
        return match($this->platform) {
            'ios' => 'fab fa-apple',
            'android' => 'fab fa-android',
            'huawei' => 'fas fa-mobile-alt',
            default => 'fas fa-mobile-alt'
        };
    }

    public function getPlatformColor(): string
    {
        return match($this->platform) {
            'ios' => 'primary',
            'android' => 'success',
            'huawei' => 'warning',
            default => 'secondary'
        };
    }

    public function generateNewAccessToken(): string
    {
        $token = Str::random(64);
        $this->update(['access_token' => $token]);
        return $token;
    }

    public function incrementDownloadCount(): void
    {
        $this->increment('download_count');
    }

    public function getDownloadUrl(): string
    {
        return route('apps.secure-download', [
            'app' => $this->id,
            'token' => $this->access_token
        ]);
    }

    public function getQrCodeUrl(): string
    {
        if ($this->qr_code_path) {
            return asset('storage/' . $this->qr_code_path);
        }
        return '';
    }

    public function getAppIconUrl(): string
    {
        if ($this->icon_path) {
            return asset('storage/' . $this->icon_path);
        }

        // Return default platform icon
        return asset('images/default-' . $this->platform . '-icon.png');
    }

    public function isQuarantined(): bool
    {
        return $this->status === 'quarantined';
    }

    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    public function canBeReleased(): bool
    {
        return $this->isQuarantined() &&
               ($this->quarantine_until === null || $this->quarantine_until->isPast());
    }

    public function getStatusBadgeClass(): string
    {
        return match($this->status) {
            'active' => 'bg-success',
            'quarantined' => 'bg-warning',
            'inactive' => 'bg-secondary',
            default => 'bg-secondary'
        };
    }

    public function getStatusText(): string
    {
        return match($this->status) {
            'active' => 'Active',
            'quarantined' => 'Quarantined',
            'inactive' => 'Inactive',
            default => 'Unknown'
        };
    }
}
