<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\App;
use App\Models\Sprint;
use App\Models\AuditLog;
use App\Models\FileAccessLog;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class AppController extends Controller
{
    /**
     * Display a listing of apps
     */
    public function index(Request $request)
    {
        $query = App::with(['sprint', 'uploader'])
                    ->fromActiveSprintsOnly(); // Only show apps from non-deleted sprints

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->get('search'));
        }

        // Filter by platform
        if ($request->filled('platform')) {
            $query->platform($request->get('platform'));
        }

        // Filter by sprint
        if ($request->filled('sprint')) {
            $query->forSprint($request->get('sprint'));
        }

        // Filter by status
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'quarantined') {
                $query->where('status', 'quarantined');
            } elseif ($status === 'active') {
                $query->where('status', 'active');
            }
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');

        if (in_array($sortBy, ['name', 'version', 'platform', 'created_at', 'download_count', 'file_size'])) {
            $query->orderBy($sortBy, $sortDirection);
        }

        $apps = $query->paginate(12)->withQueryString();

        // Get filter options
        $sprints = Sprint::active()->orderBy('name')->get();
        $platforms = ['ios', 'android', 'huawei'];

        // Get statistics
        $stats = [
            'total_apps' => App::count(),
            'total_downloads' => App::sum('download_count'),
            'total_size' => App::sum('file_size'),
            'quarantined_count' => App::where('status', 'quarantined')->count(),
            'platform_breakdown' => App::selectRaw('platform, COUNT(*) as count')
                                      ->groupBy('platform')
                                      ->pluck('count', 'platform')
                                      ->toArray(),
        ];

        // Log the view action
        AuditLog::logEvent(
            event: 'apps_viewed',
            severity: 'low',
            description: 'User viewed apps listing'
        );

        return view('apps.index', compact('apps', 'sprints', 'platforms', 'stats'));
    }

    /**
     * Show the form for creating a new app
     */
    public function create()
    {
        $sprints = Sprint::active()->orderBy('name')->get();
        return view('apps.create', compact('sprints'));
    }

    /**
     * Store a newly created app
     */
    public function store(Request $request)
    {
        $request->validate([
            'sprint_id' => 'required|exists:sprints,id',
            'temp_file_id' => 'nullable|string', // For Python server workflow
            'extracted_metadata' => 'nullable|json', // Metadata from Python server
            'file' => [
                'nullable', // Optional when using temp_file_id
                'file',
                'max:' . $this->getMaxFileSizeInKb(),
                function ($attribute, $value, $fail) use ($request) {
                    // Skip file validation if using temp_file_id workflow
                    if ($request->temp_file_id) {
                        return;
                    }
                    if ($value && !$this->isValidAppFile($value)) {
                        $fail('Invalid file type. Only IPA and APK files are allowed.');
                    }
                },
            ],
            'changelog' => 'required|string|max:2000',
            'ticket_links' => 'nullable|array',
            'ticket_links.*' => 'nullable|url|max:500',
        ]);

        // Ensure either file or temp_file_id is provided
        if (!$request->file('file') && !$request->temp_file_id) {
            return back()->withErrors(['file' => 'Please select a file to upload.']);
        }

        DB::beginTransaction();
        try {
            if ($request->temp_file_id) {
                // Handle Python server workflow with temporary file
                $extractedMetadata = json_decode($request->extracted_metadata, true);

                if (!$extractedMetadata) {
                    return back()->withErrors(['file' => 'Invalid metadata received from server.']);
                }

                // Log the metadata structure for debugging
                Log::info('Processing extracted metadata', [
                    'temp_file_id' => $request->temp_file_id,
                    'metadata_keys' => array_keys($extractedMetadata),
                    'has_plist_data' => isset($extractedMetadata['plist_data'])
                ]);

                // Check for duplicate version + build number
                $existingApp = App::where('bundle_identifier', $appInfo['bundle_identifier'])
                                 ->where('version', $appInfo['version'])
                                 ->where('build_number', $appInfo['build_number'])
                                 ->first();

                if ($existingApp) {
                    $errorMessage = "App with version {$appInfo['version']} and build {$appInfo['build_number']} already exists. Please delete the existing app or use a different version/build number.";

                    if ($request->expectsJson() || $request->ajax()) {
                        return response()->json([
                            'success' => false,
                            'message' => $errorMessage,
                            'existing_app_id' => $existingApp->id,
                            'existing_app_name' => $existingApp->name
                        ], 422);
                    }

                    return back()->withInput()->withErrors(['version' => $errorMessage]);
                }

                // Extract app information from plist_data structure
                $plistData = $extractedMetadata['plist_data']['plist_data'] ?? $extractedMetadata['plist_data'] ?? [];

                $appInfo = [
                    'name' => $plistData['CFBundleDisplayName'] ?? $plistData['CFBundleName'] ?? 'Unknown App',
                    'version' => $plistData['CFBundleShortVersionString'] ?? '1.0.0',
                    'build_number' => $plistData['CFBundleVersion'] ?? '1',
                    'bundle_identifier' => $plistData['CFBundleIdentifier'] ?? 'com.unknown.app',
                ];

                $platform = $extractedMetadata['platform'] ?? 'ios';
                $originalFilename = $extractedMetadata['original_filename'] ?? 'unknown.app';
                $fileSize = $extractedMetadata['file_size'] ?? 0;

                // Generate secure file path for permanent storage
                $fileExtension = pathinfo($originalFilename, PATHINFO_EXTENSION);
                $hash = substr(hash('sha256', $request->temp_file_id . time()), 0, 32);
                $directory = "apps/{$platform}/" . date('Y/m');
                $filePath = $directory . '/' . $hash . '.' . $fileExtension;

                // Move file from temp storage to permanent storage
                $this->moveFromTempToPermanent($request->temp_file_id, $filePath);

                // Calculate file hash for the moved file
                $fullPath = Storage::disk('private')->path($filePath);
                $fileHash = hash_file('sha256', $fullPath);

                $metadata = $extractedMetadata;

                // Handle app icon if extracted
                $iconPath = null;
                if (isset($extractedMetadata['icon_filename'])) {
                    $iconPath = 'temp_icons/' . $extractedMetadata['icon_filename'];
                }

            } else {
                // Handle direct file upload workflow (fallback)
                $file = $request->file('file');

                // Detect platform from file extension
                $platform = $this->detectPlatform($file);

                // Extract metadata from the app file
                $metadata = $this->extractAppMetadata($file, $platform);

                // Extract app information from metadata or filename
                $appInfo = $this->extractAppInfo($file, $metadata);

                // Generate secure file path
                $filePath = $this->generateSecureFilePath($file, $platform);

                // Store file in private storage
                $storedPath = Storage::disk('private')->putFileAs(
                    dirname($filePath),
                    $file,
                    basename($filePath)
                );
                $filePath = $storedPath;

                // Calculate file hash for integrity
                $fileHash = hash_file('sha256', $file->getRealPath());

                // Extract and store app icon
                $iconPath = $this->extractAndStoreIcon($file, $platform);

                $originalFilename = $file->getClientOriginalName();
                $fileSize = $file->getSize();
            }

            // Process ticket links
            $ticketLinks = array_filter($request->ticket_links ?? [], function($link) {
                return !empty(trim($link));
            });

            // Load saved metadata if available
            $savedMetadata = $this->loadSavedMetadata($request->temp_file_id);

            // Create app record
            $app = App::create([
                'name' => $appInfo['name'],
                'version' => $appInfo['version'],
                'build_number' => $appInfo['build_number'],
                'bundle_identifier' => $appInfo['bundle_identifier'],
                'platform' => $platform,
                'file_path' => $filePath,
                'file_hash' => $fileHash,
                'file_size' => $fileSize,
                'file_name' => $originalFilename,
                'mime_type' => $this->getMimeTypeFromExtension(pathinfo($originalFilename, PATHINFO_EXTENSION)),
                'icon_path' => $iconPath,
                'changelog' => $request->changelog,
                'ticket_link' => !empty($ticketLinks) ? json_encode($ticketLinks) : null,
                'metadata' => $metadata,
                'sprint_id' => $request->sprint_id,
                'uploaded_by' => auth()->id(),
                'status' => 'active', // Directly active for immediate download
            ]);

            // Move metadata to permanent storage if available
            if ($savedMetadata) {
                $this->moveAppDataToPermanentStorage($request->temp_file_id, $savedMetadata);
            }

            // Generate QR code for easy access
            $this->generateQrCode($app);

            // Log the creation
            AuditLog::logEvent(
                event: 'app_created',
                auditable: $app,
                newValues: [
                    'name' => $app->name,
                    'version' => $app->version,
                    'platform' => $app->platform,
                    'sprint_id' => $app->sprint_id,
                    'file_size' => $app->file_size,
                ],
                severity: 'medium',
                description: "App '{$app->name}' v{$app->version} uploaded for {$app->platform}"
            );

            DB::commit();

            // Check if request expects JSON (AJAX request)
            if ($request->expectsJson() || $request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => "App '{$app->name}' uploaded successfully and is ready for download!",
                    'app_id' => $app->id,
                    'redirect_url' => route('apps.show', $app)
                ]);
            }

            return redirect()->route('apps.show', $app)
                           ->with('success', "App '{$app->name}' uploaded successfully and is ready for download!");

        } catch (\Exception $e) {
            DB::rollback();

            // Clean up uploaded file if it exists
            if (isset($storedPath) && Storage::disk('private')->exists($storedPath)) {
                Storage::disk('private')->delete($storedPath);
            }

            AuditLog::logEvent(
                event: 'app_creation_failed',
                severity: 'high',
                description: 'Failed to create app',
                success: false,
                errorMessage: $e->getMessage()
            );

            // Check if request expects JSON (AJAX request)
            if ($request->expectsJson() || $request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to upload app: ' . $e->getMessage()
                ], 500);
            }

            return back()->withInput()
                        ->with('error', 'Failed to upload app: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified app
     */
    public function show(App $app)
    {
        $app->load(['sprint', 'uploader', 'fileAccessLogs' => function($query) {
            $query->orderBy('created_at', 'desc')->limit(10);
        }]);

        // Get download statistics
        $downloadStats = [
            'total_downloads' => $app->download_count,
            'successful_downloads' => $app->fileAccessLogs()->where('success', true)->count(),
            'failed_downloads' => $app->fileAccessLogs()->where('success', false)->count(),
            'unique_downloaders' => $app->fileAccessLogs()->where('success', true)->distinct('user_id')->count(),
            'recent_downloads' => $app->fileAccessLogs()->where('success', true)
                                                      ->orderBy('created_at', 'desc')
                                                      ->limit(5)
                                                      ->with('user')
                                                      ->get(),
        ];

        // Log the view action
        AuditLog::logEvent(
            event: 'app_viewed',
            auditable: $app,
            severity: 'low',
            description: "App '{$app->name}' details viewed"
        );

        return view('apps.show', compact('app', 'downloadStats'));
    }

    /**
     * Show the form for editing the specified app
     */
    public function edit(App $app)
    {
        $sprints = Sprint::active()->orderBy('name')->get();
        return view('apps.edit', compact('app', 'sprints'));
    }

    /**
     * Update the specified app
     */
    public function update(Request $request, App $app)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'version' => 'required|string|max:50',
            'build_number' => 'nullable|string|max:50',
            'bundle_identifier' => 'required|string|max:255',
            'sprint_id' => 'required|exists:sprints,id',
            'changelog' => 'nullable|string|max:2000',
            'ticket_links' => 'nullable|array',
            'ticket_links.*' => 'nullable|url|max:500',
        ]);

        try {
            // Check for duplicate version + build number (excluding current app)
            $existingApp = App::where('bundle_identifier', $request->bundle_identifier)
                             ->where('version', $request->version)
                             ->where('build_number', $request->build_number)
                             ->where('id', '!=', $app->id)
                             ->first();

            if ($existingApp) {
                $errorMessage = "Another app with version {$request->version} and build {$request->build_number} already exists. Please use a different version/build number.";
                return back()->withInput()->withErrors(['version' => $errorMessage]);
            }

            $oldValues = [
                'name' => $app->name,
                'version' => $app->version,
                'build_number' => $app->build_number,
                'bundle_identifier' => $app->bundle_identifier,
                'sprint_id' => $app->sprint_id,
                'changelog' => $app->changelog,
                'ticket_link' => $app->ticket_link,
            ];

            // Process ticket links
            $ticketLinks = array_filter($request->ticket_links ?? [], function($link) {
                return !empty(trim($link));
            });

            $app->update([
                'name' => $request->name,
                'version' => $request->version,
                'build_number' => $request->build_number,
                'bundle_identifier' => $request->bundle_identifier,
                'sprint_id' => $request->sprint_id,
                'changelog' => $request->changelog,
                'ticket_link' => !empty($ticketLinks) ? json_encode($ticketLinks) : null,
            ]);

            $newValues = [
                'name' => $app->name,
                'version' => $app->version,
                'build_number' => $app->build_number,
                'bundle_identifier' => $app->bundle_identifier,
                'sprint_id' => $app->sprint_id,
                'changelog' => $app->changelog,
                'ticket_link' => $app->ticket_link,
            ];

            // Log the update
            AuditLog::logEvent(
                event: 'app_updated',
                auditable: $app,
                oldValues: $oldValues,
                newValues: $newValues,
                severity: 'medium',
                description: "App '{$app->name}' updated"
            );

            return redirect()->route('apps.show', $app)
                           ->with('success', "App '{$app->name}' updated successfully!");

        } catch (\Exception $e) {
            AuditLog::logEvent(
                event: 'app_update_failed',
                auditable: $app,
                severity: 'high',
                description: "Failed to update app '{$app->name}'",
                success: false,
                errorMessage: $e->getMessage()
            );

            return back()->withInput()
                        ->with('error', 'Failed to update app: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified app
     */
    public function destroy(App $app)
    {
        try {
            $appName = $app->name;
            $appVersion = $app->version;

            // Delete associated files
            if ($app->file_path && Storage::disk('private')->exists($app->file_path)) {
                Storage::disk('private')->delete($app->file_path);
            }

            if ($app->icon_path && Storage::disk('public')->exists($app->icon_path)) {
                Storage::disk('public')->delete($app->icon_path);
            }

            if ($app->qr_code_path && Storage::disk('public')->exists($app->qr_code_path)) {
                Storage::disk('public')->delete($app->qr_code_path);
            }

            // Delete the app record
            $app->delete();

            // Log the deletion
            AuditLog::logEvent(
                event: 'app_deleted',
                auditable: null,
                oldValues: [
                    'name' => $appName,
                    'version' => $appVersion,
                    'platform' => $app->platform,
                    'file_size' => $app->file_size,
                ],
                severity: 'high',
                description: "App '{$appName}' v{$appVersion} deleted"
            );

            return response()->json([
                'success' => true,
                'message' => "App '{$appName}' deleted successfully!"
            ]);

        } catch (\Exception $e) {
            AuditLog::logEvent(
                event: 'app_deletion_failed',
                auditable: $app,
                severity: 'high',
                description: "Failed to delete app '{$app->name}'",
                success: false,
                errorMessage: $e->getMessage()
            );

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete app. Please try again.'
            ], 500);
        }
    }

    /**
     * Release app from quarantine
     */
    public function releaseFromQuarantine(App $app)
    {
        if (!auth()->user()->can('apps.update')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        if ($app->status !== 'quarantined') {
            return response()->json([
                'success' => false,
                'message' => 'App is not in quarantine'
            ], 422);
        }

        try {
            $app->update([
                'status' => 'active',
                'quarantine_until' => null,
                'released_by' => auth()->id(),
                'released_at' => now(),
            ]);

            AuditLog::logEvent(
                event: 'app_released_from_quarantine',
                auditable: $app,
                severity: 'medium',
                description: "App '{$app->name}' released from quarantine"
            );

            return response()->json([
                'success' => true,
                'message' => "App '{$app->name}' released from quarantine successfully!"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to release app from quarantine'
            ], 500);
        }
    }

    /**
     * Generate new access token for app
     */
    public function regenerateToken(App $app)
    {
        if (!auth()->user()->can('apps.update')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $oldToken = $app->access_token;
            $newToken = $app->generateNewAccessToken();

            AuditLog::logEvent(
                event: 'app_token_regenerated',
                auditable: $app,
                severity: 'medium',
                description: "Access token regenerated for app '{$app->name}'"
            );

            return response()->json([
                'success' => true,
                'message' => 'Access token regenerated successfully!',
                'new_token' => $newToken,
                'download_url' => $app->getDownloadUrl()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to regenerate token'
            ], 500);
        }
    }

    /**
     * Upload file chunk (bypasses POST size limits)
     */
    public function uploadChunk(Request $request)
    {
        if (!auth()->user()->can('apps.create')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $request->validate([
            'chunk' => 'required|file',
            'upload_id' => 'required|string',
            'chunk_index' => 'required|integer|min:0',
            'total_chunks' => 'required|integer|min:1',
            'original_filename' => 'required|string',
            'total_size' => 'required|integer|min:1'
        ]);

        try {
            $uploadId = $request->upload_id;
            $chunkIndex = (int) $request->chunk_index;
            $totalChunks = (int) $request->total_chunks;
            $originalFilename = $request->original_filename;
            $totalSize = (int) $request->total_size;

            // Validate file type from original filename
            $extension = strtolower(pathinfo($originalFilename, PATHINFO_EXTENSION));
            if (!in_array($extension, ['ipa', 'apk'])) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid file type. Only IPA and APK files are allowed.'
                ], 422);
            }

            // Check total file size (200MB limit)
            $maxSize = 200 * 1024 * 1024; // 200MB
            if ($totalSize > $maxSize) {
                return response()->json([
                    'success' => false,
                    'error' => 'File size exceeds 200MB limit.'
                ], 422);
            }

            // Create upload directory
            $uploadDir = storage_path("app/temp_uploads/{$uploadId}");
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Save chunk
            $chunkFile = $uploadDir . "/chunk_{$chunkIndex}";
            $request->file('chunk')->move($uploadDir, "chunk_{$chunkIndex}");

            // Create or load metadata file
            $metadataPath = $uploadDir . '/metadata.json';
            if (!file_exists($metadataPath)) {
                // Create metadata file if it doesn't exist
                $metadata = [
                    'upload_id' => $uploadId,
                    'original_filename' => $originalFilename,
                    'total_size' => $totalSize,
                    'total_chunks' => $totalChunks,
                    'uploaded_chunks' => [],
                    'created_at' => now()->toISOString()
                ];
                $result = file_put_contents($metadataPath, json_encode($metadata, JSON_PRETTY_PRINT));

                Log::info('Creating metadata file', [
                    'upload_id' => $uploadId,
                    'chunk_index' => $chunkIndex,
                    'metadata_path' => $metadataPath,
                    'result' => $result,
                    'file_exists' => file_exists($metadataPath)
                ]);
            }

            // Update metadata with uploaded chunk
            if (file_exists($metadataPath)) {
                $metadata = json_decode(file_get_contents($metadataPath), true);
                if (!in_array($chunkIndex, $metadata['uploaded_chunks'])) {
                    $metadata['uploaded_chunks'][] = $chunkIndex;
                }
                $metadata['updated_at'] = now()->toISOString();
                file_put_contents($metadataPath, json_encode($metadata, JSON_PRETTY_PRINT));
            }

            return response()->json([
                'success' => true,
                'chunk_index' => $chunkIndex,
                'uploaded_chunks' => $chunkIndex + 1,
                'total_chunks' => $totalChunks
            ]);

        } catch (\Exception $e) {
            Log::error('Chunk upload failed', [
                'error' => $e->getMessage(),
                'upload_id' => $request->upload_id,
                'chunk_index' => $request->chunk_index
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to upload chunk: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Finalize chunked upload and create final file with UUID name
     */
    public function finalizeUpload(Request $request)
    {
        if (!auth()->user()->can('apps.create')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $request->validate([
            'upload_id' => 'required|string',
            'original_filename' => 'required|string'
        ]);

        try {
            $uploadId = $request->upload_id;
            $originalFilename = $request->original_filename;
            $uploadDir = storage_path("app/temp_uploads/{$uploadId}");

            // Check if upload directory exists
            if (!is_dir($uploadDir)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Upload not found or expired.'
                ], 404);
            }

            // Load metadata
            $metadataFile = $uploadDir . '/metadata.json';

            Log::info('Finalize upload debug', [
                'upload_id' => $uploadId,
                'upload_dir' => $uploadDir,
                'metadata_file' => $metadataFile,
                'dir_exists' => is_dir($uploadDir),
                'metadata_exists' => file_exists($metadataFile),
                'dir_contents' => is_dir($uploadDir) ? scandir($uploadDir) : 'N/A'
            ]);

            if (!file_exists($metadataFile)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Upload metadata not found.',
                    'debug' => [
                        'upload_dir' => $uploadDir,
                        'metadata_file' => $metadataFile,
                        'dir_exists' => is_dir($uploadDir),
                        'dir_contents' => is_dir($uploadDir) ? scandir($uploadDir) : 'N/A'
                    ]
                ], 404);
            }

            $metadata = json_decode(file_get_contents($metadataFile), true);

            // Verify all chunks are uploaded
            $expectedChunks = range(0, $metadata['total_chunks'] - 1);
            $uploadedChunks = $metadata['uploaded_chunks'];
            sort($uploadedChunks);

            if ($expectedChunks !== $uploadedChunks) {
                return response()->json([
                    'success' => false,
                    'error' => 'Missing chunks. Upload incomplete.'
                ], 400);
            }

            // Generate UUID for final file
            $fileUuid = \Illuminate\Support\Str::uuid()->toString();
            $extension = strtolower(pathinfo($originalFilename, PATHINFO_EXTENSION));
            $finalFilename = $fileUuid . '.' . $extension;

            // Create final file directory
            $finalDir = storage_path('app/temp_files');
            if (!is_dir($finalDir)) {
                mkdir($finalDir, 0755, true);
            }

            $finalFilePath = $finalDir . '/' . $finalFilename;

            // Combine chunks into final file
            $finalFile = fopen($finalFilePath, 'wb');
            if (!$finalFile) {
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to create final file.'
                ], 500);
            }

            for ($i = 0; $i < $metadata['total_chunks']; $i++) {
                $chunkFile = $uploadDir . "/chunk_{$i}";
                if (!file_exists($chunkFile)) {
                    fclose($finalFile);
                    unlink($finalFilePath);
                    return response()->json([
                        'success' => false,
                        'error' => "Missing chunk {$i}."
                    ], 400);
                }

                $chunkData = file_get_contents($chunkFile);
                fwrite($finalFile, $chunkData);
            }

            fclose($finalFile);

            // Verify file size
            $finalFileSize = filesize($finalFilePath);
            if ($finalFileSize !== $metadata['total_size']) {
                unlink($finalFilePath);
                return response()->json([
                    'success' => false,
                    'error' => 'File size mismatch after combining chunks.'
                ], 500);
            }

            // Clean up chunks
            $this->cleanupChunks($uploadDir);

            // Store file information for metadata extraction
            $fileInfo = [
                'uuid' => $fileUuid,
                'original_filename' => $originalFilename,
                'file_path' => $finalFilePath,
                'file_size' => $finalFileSize,
                'extension' => $extension,
                'created_at' => now()->toISOString()
            ];

            $fileInfoPath = $finalDir . '/' . $fileUuid . '.info';
            file_put_contents($fileInfoPath, json_encode($fileInfo));

            return response()->json([
                'success' => true,
                'file_uuid' => $fileUuid,
                'original_filename' => $originalFilename,
                'file_size' => $finalFileSize,
                'message' => 'File uploaded and assembled successfully.'
            ]);

        } catch (\Exception $e) {
            Log::error('Finalize upload failed', [
                'error' => $e->getMessage(),
                'upload_id' => $request->upload_id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to finalize upload: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Extract metadata from uploaded file using PHP
     */
    public function extractMetadata(Request $request)
    {
        if (!auth()->user()->can('apps.create')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $request->validate([
            'file_uuid' => 'required|string',
        ]);

        try {
            $fileUuid = $request->file_uuid;
            $tempDir = storage_path('app/temp_files');
            $fileInfoPath = $tempDir . '/' . $fileUuid . '.info';

            // Check if file info exists
            if (!file_exists($fileInfoPath)) {
                return response()->json([
                    'success' => false,
                    'error' => 'File not found or expired.'
                ], 404);
            }

            // Load file information
            $fileInfo = json_decode(file_get_contents($fileInfoPath), true);
            $filePath = $fileInfo['file_path'];

            // Check if actual file exists
            if (!file_exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'error' => 'File data not found.'
                ], 404);
            }

            // Extract metadata using PHP
            $metadata = $this->extractAppMetadataFromFile($filePath, $fileInfo['original_filename'], $fileInfo['extension'], $fileUuid);

            // Debug logging
            Log::info('Metadata extraction result', [
                'file_uuid' => $fileUuid,
                'file_path' => $filePath,
                'original_filename' => $fileInfo['original_filename'],
                'extracted_metadata' => $metadata
            ]);

            return response()->json([
                'success' => true,
                'data' => $metadata
            ]);

        } catch (\Exception $e) {
            Log::error('Metadata extraction failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file_uuid' => $request->file_uuid
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to extract metadata: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get app download statistics
     */
    public function downloadStats(App $app)
    {
        if (!auth()->user()->can('apps.read')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $stats = [
            'total_downloads' => $app->download_count,
            'successful_downloads' => $app->fileAccessLogs()->where('success', true)->count(),
            'failed_downloads' => $app->fileAccessLogs()->where('success', false)->count(),
            'unique_downloaders' => $app->fileAccessLogs()->where('success', true)->distinct('user_id')->count(),
            'downloads_by_day' => $app->fileAccessLogs()
                                     ->where('success', true)
                                     ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                                     ->groupBy('date')
                                     ->orderBy('date', 'desc')
                                     ->limit(30)
                                     ->get(),
            'downloads_by_platform' => $app->fileAccessLogs()
                                          ->where('success', true)
                                          ->selectRaw('platform_detected, COUNT(*) as count')
                                          ->groupBy('platform_detected')
                                          ->get(),
        ];

        return response()->json($stats);
    }

    /**
     * Private helper methods
     */
    private function getMaxFileSizeInKb(): int
    {
        $maxSize = config('security.upload_security.max_file_size', '500MB');

        if (str_contains($maxSize, 'GB')) {
            return (int) str_replace('GB', '', $maxSize) * 1024 * 1024;
        } elseif (str_contains($maxSize, 'MB')) {
            return (int) str_replace('MB', '', $maxSize) * 1024;
        } elseif (str_contains($maxSize, 'KB')) {
            return (int) str_replace('KB', '', $maxSize);
        }

        return 512000; // 500MB default
    }

    private function isValidAppFile($file): bool
    {
        $extension = strtolower($file->getClientOriginalExtension());
        return in_array($extension, ['ipa', 'apk']);
    }

    private function extractAppInfo($file, array $metadata): array
    {
        $fileName = $file->getClientOriginalName();
        $nameWithoutExt = pathinfo($fileName, PATHINFO_FILENAME);

        // Try to extract from metadata first
        $appName = $metadata['app_name'] ?? $this->extractNameFromFilename($nameWithoutExt);
        $version = $metadata['version'] ?? $this->extractVersionFromFilename($nameWithoutExt);
        $buildNumber = $metadata['build_number'] ?? $this->extractBuildFromFilename($nameWithoutExt);
        $bundleId = $metadata['bundle_identifier'] ?? $this->generateBundleId($appName);

        return [
            'name' => $appName,
            'version' => $version,
            'build_number' => $buildNumber,
            'bundle_identifier' => $bundleId,
        ];
    }

    private function extractNameFromFilename(string $filename): string
    {
        // Remove common patterns and get the first part
        $cleaned = preg_replace('/[-_\s](v?\d+\.\d+(\.\d+)?|build\d+|\d{2,})/i', '', $filename);
        $parts = preg_split('/[-_\s]+/', $cleaned);
        return ucwords(str_replace(['-', '_'], ' ', $parts[0] ?? 'Unknown App'));
    }

    private function extractVersionFromFilename(string $filename): string
    {
        // Look for version patterns like 1.0.0, v1.2.3, etc.
        if (preg_match('/v?(\d+\.\d+(?:\.\d+)?)/i', $filename, $matches)) {
            return $matches[1];
        }
        return '1.0.0';
    }

    private function extractBuildFromFilename(string $filename): string
    {
        // Look for build patterns like build123, 123, etc.
        if (preg_match('/(?:build)?(\d{2,})/i', $filename, $matches)) {
            return $matches[1];
        }
        return '1';
    }

    private function generateBundleId(string $appName): string
    {
        $cleanName = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $appName));
        return "com.company.{$cleanName}";
    }

    private function extractAppMetadata($file, string $platform): array
    {
        $metadata = [
            'extracted_at' => now(),
            'file_type' => $file->getMimeType(),
            'original_name' => $file->getClientOriginalName(),
        ];

        try {
            if ($platform === 'android' || $platform === 'huawei') {
                $metadata = array_merge($metadata, $this->extractApkMetadata($file));
            } elseif ($platform === 'ios') {
                $metadata = array_merge($metadata, $this->extractIpaMetadata($file));
            }
        } catch (\Exception $e) {
            Log::warning('Failed to extract app metadata', [
                'platform' => $platform,
                'error' => $e->getMessage()
            ]);
            $metadata['extraction_error'] = $e->getMessage();
        }

        return $metadata;
    }





    private function generateSecureFilePath($file, string $platform): string
    {
        $extension = $file->getClientOriginalExtension();
        $hash = hash('sha256', $file->getClientOriginalName() . time() . auth()->id());
        $directory = "apps/{$platform}/" . date('Y/m');

        return $directory . '/' . $hash . '.' . $extension;
    }

    private function extractAndStoreIcon($file, string $platform): ?string
    {
        try {
            // This is a simplified icon extraction
            // In a real implementation, you'd use specialized libraries
            // to extract icons from APK/IPA files

            $iconDirectory = 'icons/' . date('Y/m');
            $iconFilename = Str::random(40) . '.png';
            $iconPath = $iconDirectory . '/' . $iconFilename;

            // For now, we'll create a placeholder icon
            // In production, implement actual icon extraction
            $this->createPlaceholderIcon($platform, $iconPath);

            return $iconPath;

        } catch (\Exception $e) {
            Log::warning('Failed to extract app icon', [
                'platform' => $platform,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    private function createPlaceholderIcon(string $platform, string $iconPath): void
    {
        // Create a simple placeholder icon based on platform
        // In production, implement actual icon extraction from app files
        $iconColor = match ($platform) {
            'ios' => '#007AFF',
            'android' => '#3DDC84',
            'huawei' => '#FF6B35',
            default => '#6C757D'
        };

        // This would create an actual icon file
        // For now, we'll just ensure the directory exists
        Storage::disk('public')->makeDirectory(dirname($iconPath));
    }

    private function generateQrCode(App $app): void
    {
        try {
            // Generate QR code for the app download URL
            // In production, use a QR code library like SimpleSoftwareIO/simple-qrcode

            $qrDirectory = 'qr-codes/' . date('Y/m');
            $qrFilename = 'qr_' . $app->id . '_' . Str::random(20) . '.png';
            $qrPath = $qrDirectory . '/' . $qrFilename;

            // Ensure directory exists
            Storage::disk('public')->makeDirectory($qrDirectory);

            // Update app with QR code path
            $app->update(['qr_code_path' => $qrPath]);

        } catch (\Exception $e) {
            Log::warning('Failed to generate QR code', [
                'app_id' => $app->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    private function detectPlatform($file): string
    {
        $extension = strtolower($file->getClientOriginalExtension());

        return match ($extension) {
            'apk' => 'android',
            'ipa' => 'ios',
            default => 'unknown'
        };
    }

    private function parseSize(string $size): int
    {
        $size = trim($size);
        $last = strtolower($size[strlen($size) - 1]);
        $size = (int) $size;

        switch ($last) {
            case 'g':
                $size *= 1024;
            case 'm':
                $size *= 1024;
            case 'k':
                $size *= 1024;
        }

        return $size;
    }

    private function formatBytes(int $bytes): string
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    private function moveFromTempToPermanent(string $tempFileId, string $finalPath): void
    {
        try {
            // Source file in temp_files directory
            $tempFilePath = storage_path('app/temp_files/' . $tempFileId . '.ipa');

            if (!file_exists($tempFilePath)) {
                // Try other extensions
                $tempFilePath = storage_path('app/temp_files/' . $tempFileId . '.apk');
                if (!file_exists($tempFilePath)) {
                    throw new \Exception('Temp file not found: ' . $tempFileId);
                }
            }

            // Ensure destination directory exists
            $destinationDir = dirname(Storage::disk('private')->path($finalPath));
            if (!is_dir($destinationDir)) {
                mkdir($destinationDir, 0755, true);
            }

            // Move file to permanent storage
            $destinationPath = Storage::disk('private')->path($finalPath);

            if (!copy($tempFilePath, $destinationPath)) {
                throw new \Exception('Failed to copy file from temp to permanent storage');
            }

            // Remove temp file after successful copy
            unlink($tempFilePath);

            Log::info('Successfully moved file from temp to permanent storage', [
                'temp_file_id' => $tempFileId,
                'from' => $tempFilePath,
                'to' => $destinationPath
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to move file from temp to permanent storage', [
                'temp_file_id' => $tempFileId,
                'final_path' => $finalPath,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    private function getMimeTypeFromExtension(string $extension): string
    {
        return match (strtolower($extension)) {
            'ipa' => 'application/octet-stream',
            'apk' => 'application/vnd.android.package-archive',
            default => 'application/octet-stream'
        };
    }



    private function cleanupChunks($uploadDir): void
    {
        try {
            // Remove all chunk files
            $files = glob($uploadDir . '/chunk_*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }

            // Remove metadata file
            $metadataFile = $uploadDir . '/metadata.json';
            if (file_exists($metadataFile)) {
                unlink($metadataFile);
            }

            // Remove upload directory if empty
            if (is_dir($uploadDir) && count(scandir($uploadDir)) === 2) { // Only . and ..
                rmdir($uploadDir);
            }
        } catch (\Exception $e) {
            Log::warning('Failed to cleanup chunks', [
                'upload_dir' => $uploadDir,
                'error' => $e->getMessage()
            ]);
        }
    }

    private function extractAppMetadataFromFile($filePath, $originalFilename, $extension, $fileUuid = null): array
    {
        try {
            $metadata = [
                'platform' => $this->detectPlatformFromExtension($extension),
                'file_type' => $extension,
                'extraction_method' => 'php_zip',
                'original_filename' => $originalFilename,
                'file_size' => filesize($filePath),
                'app_name' => 'Unknown App',
                'version' => '1.0.0',
                'build_number' => '1',
                'bundle_identifier' => 'com.unknown.app',
                'minimum_os_version' => 'Unknown',
                'icon_path' => null
            ];

            if ($extension === 'ipa') {
                $ipaMetadata = $this->extractIpaMetadata($filePath, $fileUuid);
                $metadata = array_merge($metadata, $ipaMetadata);
            } elseif ($extension === 'apk') {
                $apkMetadata = $this->extractApkMetadata($filePath);
                $metadata = array_merge($metadata, $apkMetadata);
            }

            return $metadata;

        } catch (\Exception $e) {
            Log::error('Failed to extract metadata from file', [
                'error' => $e->getMessage(),
                'file_path' => $filePath,
                'extension' => $extension
            ]);

            return [
                'app_name' => 'Unknown App',
                'version' => '1.0.0',
                'build_number' => '1',
                'bundle_identifier' => 'com.unknown.app',
                'platform' => $this->detectPlatformFromExtension($extension),
                'extraction_error' => $e->getMessage()
            ];
        }
    }

    private function detectPlatformFromExtension(string $extension): string
    {
        return match (strtolower($extension)) {
            'ipa' => 'ios',
            'apk' => 'android',
            default => 'unknown'
        };
    }

    private function extractIpaMetadata($filePath, $fileUuid = null): array
    {
        $metadata = [
            'platform' => 'ios',
            'extraction_method' => 'info_plist'
        ];

        try {
            $zip = new \ZipArchive();
            $result = $zip->open($filePath);

            if ($result !== TRUE) {
                throw new \Exception("Failed to open IPA file: " . $result);
            }

            Log::info('IPA extraction started', [
                'file_path' => $filePath,
                'num_files' => $zip->numFiles
            ]);

            // Debug: List first 10 files
            $fileList = [];
            for ($i = 0; $i < min(10, $zip->numFiles); $i++) {
                $fileList[] = $zip->getNameIndex($i);
            }
            Log::info('First 10 files in IPA', $fileList);

            // Find Info.plist in Payload directory
            $infoPlistPath = null;
            for ($i = 0; $i < $zip->numFiles; $i++) {
                $fileName = $zip->getNameIndex($i);
                if (preg_match('/^Payload\/[^\/]+\.app\/Info\.plist$/', $fileName)) {
                    $infoPlistPath = $fileName;
                    Log::info('Found Info.plist', ['path' => $infoPlistPath]);
                    break;
                }
            }

            if ($infoPlistPath) {
                // Extract and parse Info.plist
                $plistContent = $zip->getFromName($infoPlistPath);
                if ($plistContent !== false) {
                    Log::info('Info.plist content extracted', [
                        'size' => strlen($plistContent),
                        'is_xml' => strpos($plistContent, '<?xml') !== false,
                        'first_100_chars' => substr($plistContent, 0, 100)
                    ]);

                    // Log the complete plist content for debugging
                    Log::info('Complete Info.plist content (string)', [
                        'full_content' => $plistContent
                    ]);

                    $plistData = $this->parsePlist($plistContent, $fileUuid);

                    Log::info('Parsed plist data', [
                        'plist_data' => $plistData,
                        'keys_found' => $plistData ? array_keys($plistData) : 'null',
                        'data_type' => gettype($plistData),
                        'is_array' => is_array($plistData),
                        'count' => is_array($plistData) ? count($plistData) : 0
                    ]);

                    if ($plistData && is_array($plistData)) {
                        // Only return the raw plist data - let frontend extract what it needs
                        $metadata['plist_data'] = $plistData;

                        Log::info('Successfully extracted IPA metadata', [
                            'plist_keys_found' => array_keys($plistData),
                            'plist_data_size' => count($plistData)
                        ]);
                    } else {
                        Log::warning('Failed to parse plist data', [
                            'plist_data_type' => gettype($plistData),
                            'plist_data' => $plistData
                        ]);
                        $metadata['extraction_error'] = 'Failed to parse Info.plist - no valid data found';
                    }
                } else {
                    Log::error('Failed to read Info.plist content');
                    $metadata['extraction_error'] = 'Failed to read Info.plist content';
                }
            } else {
                Log::warning('Info.plist not found in Payload directory');
                $metadata['extraction_error'] = 'Info.plist not found in Payload directory';
            }

            // Try to extract app icon
            $iconPath = $this->extractIpaIcon($zip);
            if ($iconPath) {
                $metadata['icon_path'] = $iconPath;
            }

            $zip->close();

        } catch (\Exception $e) {
            $metadata['extraction_error'] = 'Failed to extract IPA metadata: ' . $e->getMessage();
        }

        return $metadata;
    }

    private function extractApkMetadata($filePath): array
    {
        $metadata = [
            'platform' => 'android',
            'extraction_method' => 'android_manifest'
        ];

        try {
            $zip = new \ZipArchive();
            $result = $zip->open($filePath);

            if ($result !== TRUE) {
                throw new \Exception("Failed to open APK file: " . $result);
            }

            // Find AndroidManifest.xml
            $manifestContent = $zip->getFromName('AndroidManifest.xml');
            if ($manifestContent !== false) {
                // AndroidManifest.xml is binary, try to extract basic info
                $metadata['has_manifest'] = true;

                // Try to find strings.xml or other readable XML files
                $this->extractApkStringsInfo($zip, $metadata);
            } else {
                $metadata['extraction_error'] = 'AndroidManifest.xml not found';
            }

            $zip->close();

        } catch (\Exception $e) {
            $metadata['extraction_error'] = 'Failed to extract APK metadata: ' . $e->getMessage();
        }

        return $metadata;
    }

    private function extractApkStringsInfo($zip, &$metadata): void
    {
        try {
            // Look for strings.xml files
            for ($i = 0; $i < $zip->numFiles; $i++) {
                $fileName = $zip->getNameIndex($i);

                // Look for strings.xml in res/values
                if (preg_match('/res\/values.*\/strings\.xml$/', $fileName)) {
                    $stringsContent = $zip->getFromName($fileName);
                    if ($stringsContent !== false) {
                        $this->parseApkStringsXml($stringsContent, $metadata);
                        break;
                    }
                }
            }
        } catch (\Exception $e) {
            $metadata['strings_error'] = $e->getMessage();
        }
    }

    private function parseApkStringsXml($xmlContent, &$metadata): void
    {
        try {
            $xml = simplexml_load_string($xmlContent);
            if ($xml !== false) {
                foreach ($xml->string as $string) {
                    $name = (string) $string['name'];
                    $value = (string) $string;

                    // Common app name patterns
                    if (in_array($name, ['app_name', 'application_name', 'title'])) {
                        $metadata['app_name'] = $value;
                    }
                }
            }
        } catch (\Exception $e) {
            $metadata['xml_parse_error'] = $e->getMessage();
        }
    }

    private function parsePlist($plistContent, $fileUuid = null): ?array
    {
        try {
            // Check if it's XML plist (not binary)
            if (str_contains($plistContent, '<?xml') && str_contains($plistContent, '<plist')) {
                Log::info('Parsing XML plist', [
                    'content_length' => strlen($plistContent),
                    'first_200_chars' => substr($plistContent, 0, 200)
                ]);

                // Clean up the XML content
                $cleanContent = trim($plistContent);

                // Parse XML plist
                libxml_use_internal_errors(true);
                $xml = simplexml_load_string($cleanContent);

                if ($xml === false) {
                    $errors = libxml_get_errors();
                    Log::error('XML parsing failed', [
                        'errors' => array_map(function($error) {
                            return $error->message;
                        }, $errors)
                    ]);
                    libxml_clear_errors();
                    return null;
                }

                if (isset($xml->dict)) {
                    Log::info('Found dict element in plist', [
                        'dict_children_count' => count($xml->dict->children()),
                        'xml_structure' => $xml->asXML()
                    ]);
                    $result = $this->parsePlistDict($xml->dict);
                    Log::info('Parsed plist result', [
                        'result' => $result,
                        'result_keys' => is_array($result) ? array_keys($result) : 'not_array'
                    ]);
                    return $result;
                } else {
                    Log::warning('No dict element found in plist XML', [
                        'xml_children' => $xml->children(),
                        'xml_structure' => $xml->asXML()
                    ]);
                    return null;
                }
            } else {
                Log::warning('Binary plist detected - using Python parser');
                return $this->parseBinaryPlistWithPython($plistContent, $fileUuid);
            }
        } catch (\Exception $e) {
            Log::error('Failed to parse plist', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return null;
    }

    private function parsePlistDict($dict): array
    {
        $result = [];
        $children = $dict->children();
        $childArray = [];

        // Convert SimpleXML children to array for easier processing
        foreach ($children as $child) {
            $childArray[] = $child;
        }

        Log::info('Parsing plist dict', [
            'children_count' => count($childArray),
            'first_few_elements' => array_slice(array_map(function($child) {
                return ['name' => $child->getName(), 'value' => (string)$child];
            }, $childArray), 0, 10)
        ]);

        // Process key-value pairs
        for ($i = 0; $i < count($childArray); $i++) {
            $child = $childArray[$i];

            if ($child->getName() === 'key') {
                $key = (string) $child;

                // Look for the next non-key element as the value
                $valueIndex = $i + 1;
                if ($valueIndex < count($childArray)) {
                    $valueElement = $childArray[$valueIndex];
                    $result[$key] = $this->parsePlistValue($valueElement);
                    $i++; // Skip the value element in next iteration
                }
            }
        }

        Log::info('Parsed plist dict result', [
            'keys_found' => array_keys($result),
            'sample_values' => array_slice($result, 0, 5, true)
        ]);

        return $result;
    }

    private function parsePlistValue($element)
    {
        $tagName = $element->getName();

        switch ($tagName) {
            case 'string':
                return (string) $element;

            case 'integer':
                return (int) $element;

            case 'real':
                return (float) $element;

            case 'true':
                return true;

            case 'false':
                return false;

            case 'array':
                $arrayValues = [];
                foreach ($element->children() as $arrayItem) {
                    $arrayValues[] = $this->parsePlistValue($arrayItem);
                }
                return $arrayValues;

            case 'dict':
                return $this->parsePlistDict($element);

            case 'data':
                return base64_decode((string) $element);

            case 'date':
                return (string) $element;

            default:
                // Fallback to string representation
                return (string) $element;
        }
    }

    private function extractIpaIcon($zip): ?string
    {
        try {
            // Look for app icons in the app bundle
            for ($i = 0; $i < $zip->numFiles; $i++) {
                $fileName = $zip->getNameIndex($i);

                // Look for icon files in the app bundle
                if (preg_match('/^Payload\/[^\/]+\.app\/.*[Ii]con.*\.(png|jpg|jpeg)$/', $fileName)) {
                    // Extract icon to public storage
                    $iconContent = $zip->getFromName($fileName);
                    if ($iconContent !== false) {
                        $iconName = 'app_icons/' . uniqid() . '.png';
                        $iconPath = storage_path('app/public/' . $iconName);

                        // Create directory if it doesn't exist
                        $iconDir = dirname($iconPath);
                        if (!is_dir($iconDir)) {
                            mkdir($iconDir, 0755, true);
                        }

                        file_put_contents($iconPath, $iconContent);
                        return $iconName;
                    }
                }
            }
        } catch (\Exception $e) {
            Log::warning('Failed to extract IPA icon', ['error' => $e->getMessage()]);
        }

        return null;
    }

    private function extractFromBinaryPlist($binaryContent): ?array
    {
        Log::info('Attempting to extract from binary plist');

        // For binary plist, try to extract readable strings
        $result = [];

        // Common iOS app bundle keys we're looking for
        $keys = [
            'CFBundleName',
            'CFBundleDisplayName',
            'CFBundleIdentifier',
            'CFBundleShortVersionString',
            'CFBundleVersion',
            'MinimumOSVersion',
            'CFBundleExecutable'
        ];

        foreach ($keys as $key) {
            $value = $this->extractValueAfterKey($binaryContent, $key);
            if ($value) {
                $result[$key] = $value;
            }
        }

        Log::info('Binary plist extraction result', $result);

        return !empty($result) ? $result : null;
    }

    private function extractValueAfterKey($content, $key): ?string
    {
        $keyPos = strpos($content, $key);
        if ($keyPos === false) {
            return null;
        }

        // Look for a string value after the key
        $afterKey = substr($content, $keyPos + strlen($key));

        // Try to find printable ASCII strings
        if (preg_match('/[\x20-\x7E]{2,100}/', $afterKey, $matches)) {
            $candidate = trim($matches[0]);

            // Filter out common non-value strings
            $excludePatterns = [
                '/^[0-9.]+$/',  // Just numbers
                '/^[A-Z_]+$/',  // Just uppercase constants
                '/^\W+$/',      // Just symbols
            ];

            $isExcluded = false;
            foreach ($excludePatterns as $pattern) {
                if (preg_match($pattern, $candidate)) {
                    $isExcluded = true;
                    break;
                }
            }

            if ($isExcluded) {
                return null;
            }

            // If it looks like a reasonable value, return it
            if (strlen($candidate) >= 2 && strlen($candidate) <= 100) {
                return $candidate;
            }
        }

        return null;
    }



    private function parseBinaryPlistWithPython($plistContent, $fileUuid = null): ?array
    {
        try {
            // Create temporary file for the plist content
            $tempFile = tempnam(sys_get_temp_dir(), 'info_plist_');
            file_put_contents($tempFile, $plistContent);

            // Path to Python script
            $scriptPath = base_path('scripts/parse_plist.py');

            // Create output file path
            $outputFile = $tempFile . '_extracted.json';

            Log::info('Using Python parser for binary plist', [
                'temp_file' => $tempFile,
                'script_path' => $scriptPath,
                'output_file' => $outputFile
            ]);

            // Get the original IPA file path for icon extraction
            $ipaFilePath = $this->getIpaFilePathFromContext($fileUuid);

            // Execute Python script with IPA file path and UUID for icon extraction
            $command = "python3 " . escapeshellarg($scriptPath) . " " . escapeshellarg($tempFile) . " " . escapeshellarg($outputFile);
            if ($ipaFilePath && $fileUuid) {
                $command .= " " . escapeshellarg($ipaFilePath) . " " . escapeshellarg($fileUuid);
            }
            $command .= " 2>&1";
            $output = shell_exec($command);

            Log::info('Python script output', [
                'command' => $command,
                'output' => $output
            ]);

            // Extract JSON from output
            if (preg_match('/=== JSON_OUTPUT_START ===(.*)=== JSON_OUTPUT_END ===/s', $output, $matches)) {
                $jsonData = trim($matches[1]);
                $result = json_decode($jsonData, true);

                if ($result && $result['success']) {
                    Log::info('Successfully parsed binary plist with Python', [
                        'plist_keys_found' => isset($result['raw_data']) ? array_keys($result['raw_data']) : [],
                        'extraction_method' => $result['extraction_method'] ?? 'unknown'
                    ]);

                    // Save extracted data to temp location with same UUID
                    $this->saveExtractedAppDataToTemp($result, $fileUuid);

                    // Clean up temp files
                    unlink($tempFile);
                    if (file_exists($outputFile)) {
                        unlink($outputFile);
                    }

                    // Return only the raw plist data
                    return [
                        'platform' => 'ios',
                        'extraction_method' => $result['extraction_method'] ?? 'python_plistlib',
                        'plist_data' => $result['raw_data'] ?? []
                    ];
                } else {
                    Log::error('Python script failed', [
                        'result' => $result,
                        'json_data' => $jsonData
                    ]);
                }
            } else {
                Log::error('Could not extract JSON from Python output', [
                    'output' => $output
                ]);
            }

            // Clean up temp files
            unlink($tempFile);
            if (file_exists($outputFile)) {
                unlink($outputFile);
            }

        } catch (\Exception $e) {
            Log::error('Error running Python plist parser', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return null;
    }

    private function saveExtractedAppDataToTemp($appData, $fileUuid): void
    {
        try {
            if (!$fileUuid) {
                Log::warning('No file UUID provided for saving extracted app data');
                return;
            }

            // Save to temp_files directory with UUID as filename
            $tempDir = storage_path('app/temp_files');
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            $metadataFile = $tempDir . '/' . $fileUuid . '_metadata.json';
            file_put_contents($metadataFile, json_encode($appData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

            Log::info('Saved extracted app data to temp location', [
                'file_uuid' => $fileUuid,
                'app_name' => $appData['app_name'] ?? 'Unknown',
                'bundle_id' => $appData['bundle_identifier'] ?? 'unknown',
                'version' => $appData['version'] ?? '1.0.0',
                'saved_to' => $metadataFile
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to save extracted app data to temp', [
                'error' => $e->getMessage(),
                'file_uuid' => $fileUuid,
                'app_data' => $appData
            ]);
        }
    }

    private function moveAppDataToPermanentStorage($fileUuid, $appData): void
    {
        try {
            // Create permanent storage directory
            $appName = $appData['app_name'] ?? 'Unknown';
            $bundleId = $appData['bundle_identifier'] ?? 'unknown';
            $version = $appData['version'] ?? '1.0.0';

            // Sanitize filename
            $sanitizedAppName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $appName);
            $sanitizedBundleId = preg_replace('/[^a-zA-Z0-9._-]/', '_', $bundleId);

            $permanentDir = storage_path('app/extracted_app_data');
            if (!is_dir($permanentDir)) {
                mkdir($permanentDir, 0755, true);
            }

            // Create descriptive filename
            $filename = sprintf(
                '%s_%s_v%s_%s.json',
                $sanitizedAppName,
                $sanitizedBundleId,
                $version,
                date('Y-m-d_H-i-s')
            );

            // Copy temp metadata file to permanent location
            $tempMetadataFile = storage_path('app/temp_files/' . $fileUuid . '_metadata.json');
            $permanentMetadataFile = $permanentDir . '/' . $filename;

            if (file_exists($tempMetadataFile)) {
                copy($tempMetadataFile, $permanentMetadataFile);
                unlink($tempMetadataFile); // Remove temp file

                Log::info('Moved app metadata to permanent storage', [
                    'file_uuid' => $fileUuid,
                    'from' => $tempMetadataFile,
                    'to' => $permanentMetadataFile
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Failed to move app data to permanent storage', [
                'error' => $e->getMessage(),
                'file_uuid' => $fileUuid
            ]);
        }
    }

    private function loadSavedMetadata($fileUuid): ?array
    {
        try {
            $metadataFile = storage_path('app/temp_files/' . $fileUuid . '_metadata.json');

            if (file_exists($metadataFile)) {
                $content = file_get_contents($metadataFile);
                $metadata = json_decode($content, true);

                Log::info('Loaded saved metadata for app creation', [
                    'file_uuid' => $fileUuid,
                    'metadata_file' => $metadataFile,
                    'app_name' => $metadata['app_name'] ?? 'Unknown'
                ]);

                return $metadata;
            }

            Log::info('No saved metadata found', [
                'file_uuid' => $fileUuid,
                'expected_file' => $metadataFile
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to load saved metadata', [
                'file_uuid' => $fileUuid,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    private function getIpaFilePathFromContext($fileUuid): ?string
    {
        try {
            // The IPA file should be in temp_files with the UUID
            $ipaPath = storage_path('app/temp_files/' . $fileUuid . '.ipa');

            if (file_exists($ipaPath)) {
                return $ipaPath;
            }

            Log::warning('IPA file not found for icon extraction', [
                'file_uuid' => $fileUuid,
                'expected_path' => $ipaPath
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting IPA file path', [
                'file_uuid' => $fileUuid,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }
}
