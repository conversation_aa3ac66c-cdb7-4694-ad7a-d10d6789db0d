<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use App\Models\AuditLog;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class RoleController extends Controller
{
    // Middleware will be applied via routes

    /**
     * Display a listing of roles
     */
    public function index(Request $request)
    {
        $query = Role::withCount(['users', 'permissions']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where('name', 'like', "%{$search}%");
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');

        if (in_array($sortBy, ['name', 'users_count', 'permissions_count', 'created_at'])) {
            $query->orderBy($sortBy, $sortDirection);
        }

        $roles = $query->paginate(15)->withQueryString();

        // Log the view action
        AuditLog::logEvent(
            event: 'roles_viewed',
            severity: 'low',
            description: 'User viewed roles listing'
        );

        return view('roles.index', compact('roles'));
    }

    /**
     * Show the form for creating a new role
     */
    public function create()
    {
        $permissions = Permission::all()->groupBy(function ($permission) {
            return explode('.', $permission->name)[0]; // Group by module
        });

        return view('roles.create', compact('permissions'));
    }

    /**
     * Store a newly created role
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        DB::beginTransaction();
        try {
            // Create the role
            $role = Role::create([
                'name' => $request->name,
                'guard_name' => 'web',
            ]);

            // Assign permissions
            if ($request->filled('permissions')) {
                $role->givePermissionTo($request->permissions);
            }

            // Log the creation
            AuditLog::logEvent(
                event: 'role_created',
                auditable: $role,
                newValues: [
                    'name' => $role->name,
                    'permissions' => $request->permissions ?? [],
                ],
                severity: 'medium',
                description: "Role '{$role->name}' created with " . count($request->permissions ?? []) . " permissions"
            );

            DB::commit();

            return redirect()->route('roles.index')
                           ->with('success', "Role '{$role->name}' created successfully!");

        } catch (\Exception $e) {
            DB::rollback();

            AuditLog::logEvent(
                event: 'role_creation_failed',
                severity: 'high',
                description: 'Failed to create role',
                success: false,
                errorMessage: $e->getMessage()
            );

            return back()->withInput()
                        ->with('error', 'Failed to create role. Please try again.');
        }
    }

    /**
     * Display the specified role
     */
    public function show(Role $role)
    {
        $role->load(['permissions', 'users']);

        // Get users with this role
        $users = $role->users()->paginate(10);

        // Group permissions by module
        $permissionsByModule = $role->permissions->groupBy(function ($permission) {
            return explode('.', $permission->name)[0];
        });

        AuditLog::logEvent(
            event: 'role_viewed',
            auditable: $role,
            severity: 'low',
            description: "Role '{$role->name}' details viewed"
        );

        return view('roles.show', compact('role', 'users', 'permissionsByModule'));
    }

    /**
     * Show the form for editing the specified role
     */
    public function edit(Role $role)
    {
        // Prevent editing system roles
        if (in_array($role->name, ['Super Admin'])) {
            return redirect()->route('roles.index')
                           ->with('warning', 'System roles cannot be edited.');
        }

        $permissions = Permission::all()->groupBy(function ($permission) {
            return explode('.', $permission->name)[0];
        });

        $rolePermissions = $role->permissions->pluck('name')->toArray();

        return view('roles.edit', compact('role', 'permissions', 'rolePermissions'));
    }

    /**
     * Update the specified role
     */
    public function update(Request $request, Role $role)
    {
        // Prevent editing system roles
        if (in_array($role->name, ['Super Admin'])) {
            return redirect()->route('roles.index')
                           ->with('warning', 'System roles cannot be modified.');
        }

        $request->validate([
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('roles', 'name')->ignore($role->id),
            ],
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        DB::beginTransaction();
        try {
            $oldValues = [
                'name' => $role->name,
                'permissions' => $role->permissions->pluck('name')->toArray(),
            ];

            // Update role name
            $role->update(['name' => $request->name]);

            // Sync permissions
            $role->syncPermissions($request->permissions ?? []);

            $newValues = [
                'name' => $role->name,
                'permissions' => $request->permissions ?? [],
            ];

            // Log the update
            AuditLog::logEvent(
                event: 'role_updated',
                auditable: $role,
                oldValues: $oldValues,
                newValues: $newValues,
                severity: 'medium',
                description: "Role '{$role->name}' updated"
            );

            DB::commit();

            return redirect()->route('roles.index')
                           ->with('success', "Role '{$role->name}' updated successfully!");

        } catch (\Exception $e) {
            DB::rollback();

            AuditLog::logEvent(
                event: 'role_update_failed',
                auditable: $role,
                severity: 'high',
                description: "Failed to update role '{$role->name}'",
                success: false,
                errorMessage: $e->getMessage()
            );

            return back()->withInput()
                        ->with('error', 'Failed to update role. Please try again.');
        }
    }

    /**
     * Remove the specified role
     */
    public function destroy(Role $role)
    {
        // Prevent deleting system roles
        if (in_array($role->name, ['Super Admin', 'Admin'])) {
            return response()->json([
                'success' => false,
                'message' => 'System roles cannot be deleted.'
            ], 403);
        }

        // Check if role has users
        if ($role->users()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete role that has assigned users. Please reassign users first.'
            ], 422);
        }

        DB::beginTransaction();
        try {
            $roleName = $role->name;
            $permissions = $role->permissions->pluck('name')->toArray();

            // Delete the role
            $role->delete();

            // Log the deletion
            AuditLog::logEvent(
                event: 'role_deleted',
                auditable: null,
                oldValues: [
                    'name' => $roleName,
                    'permissions' => $permissions,
                ],
                severity: 'high',
                description: "Role '{$roleName}' deleted"
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "Role '{$roleName}' deleted successfully!"
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            AuditLog::logEvent(
                event: 'role_deletion_failed',
                auditable: $role,
                severity: 'high',
                description: "Failed to delete role '{$role->name}'",
                success: false,
                errorMessage: $e->getMessage()
            );

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete role. Please try again.'
            ], 500);
        }
    }

    /**
     * Get role permissions for AJAX requests
     */
    public function permissions(Role $role)
    {
        if (!auth()->user()->can('roles.read')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        return response()->json([
            'permissions' => $role->permissions->pluck('name')
        ]);
    }

    /**
     * Assign role to user
     */
    public function assignUser(Request $request, Role $role)
    {
        if (!auth()->user()->can('roles.update')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $user = User::findOrFail($request->user_id);

        // Check if user already has this role
        if ($user->hasRole($role)) {
            return response()->json([
                'success' => false,
                'message' => 'User already has this role.'
            ], 422);
        }

        try {
            $user->assignRole($role);

            AuditLog::logEvent(
                event: 'role_assigned',
                auditable: $user,
                newValues: [
                    'role' => $role->name,
                    'user' => $user->name,
                ],
                severity: 'medium',
                description: "Role '{$role->name}' assigned to user '{$user->name}'"
            );

            return response()->json([
                'success' => true,
                'message' => "Role '{$role->name}' assigned to '{$user->name}' successfully!"
            ]);

        } catch (\Exception $e) {
            AuditLog::logEvent(
                event: 'role_assignment_failed',
                auditable: $user,
                severity: 'high',
                description: "Failed to assign role '{$role->name}' to user '{$user->name}'",
                success: false,
                errorMessage: $e->getMessage()
            );

            return response()->json([
                'success' => false,
                'message' => 'Failed to assign role. Please try again.'
            ], 500);
        }
    }

    /**
     * Remove role from user
     */
    public function removeUser(Request $request, Role $role)
    {
        if (!auth()->user()->can('roles.update')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $user = User::findOrFail($request->user_id);

        // Prevent removing last Super Admin
        if ($role->name === 'Super Admin' && $role->users()->count() <= 1) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot remove the last Super Admin user.'
            ], 422);
        }

        try {
            $user->removeRole($role);

            AuditLog::logEvent(
                event: 'role_removed',
                auditable: $user,
                oldValues: [
                    'role' => $role->name,
                    'user' => $user->name,
                ],
                severity: 'medium',
                description: "Role '{$role->name}' removed from user '{$user->name}'"
            );

            return response()->json([
                'success' => true,
                'message' => "Role '{$role->name}' removed from '{$user->name}' successfully!"
            ]);

        } catch (\Exception $e) {
            AuditLog::logEvent(
                event: 'role_removal_failed',
                auditable: $user,
                severity: 'high',
                description: "Failed to remove role '{$role->name}' from user '{$user->name}'",
                success: false,
                errorMessage: $e->getMessage()
            );

            return response()->json([
                'success' => false,
                'message' => 'Failed to remove role. Please try again.'
            ], 500);
        }
    }
}
