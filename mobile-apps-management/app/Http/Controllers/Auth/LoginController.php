<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use App\Models\User;
use App\Models\AuditLog;

class LoginController extends Controller
{
    /**
     * Show the login form
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * Handle login attempt
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        $user = User::where('email', $request->email)->first();

        // Check if user exists
        if (!$user) {
            AuditLog::logLogin(
                user: new User(['email' => $request->email]),
                success: false,
                errorMessage: 'User not found'
            );

            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        // Check if user is active
        if (!$user->is_active) {
            AuditLog::logLogin($user, false, 'Account is inactive');

            throw ValidationException::withMessages([
                'email' => ['Your account has been deactivated.'],
            ]);
        }

        // Check if user is locked
        if ($user->isLocked()) {
            AuditLog::logLogin($user, false, 'Account is locked');

            $lockUntil = $user->locked_until->format('Y-m-d H:i:s');
            throw ValidationException::withMessages([
                'email' => ["Your account is locked until {$lockUntil}."],
            ]);
        }

        // Verify password
        if (!Hash::check($request->password, $user->password)) {
            $user->incrementFailedLogins();

            AuditLog::logLogin($user, false, 'Invalid password');

            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        // Check if password needs to be changed
        if ($user->needsPasswordChange()) {
            session(['password_change_required' => true, 'user_id' => $user->id]);
            return redirect()->route('password.change.form')
                           ->with('warning', 'Your password has expired and must be changed.');
        }

        // Successful login
        $user->resetFailedLogins();
        Auth::login($user, $request->boolean('remember'));

        AuditLog::logLogin($user, true);

        $request->session()->regenerate();

        return redirect()->intended(route('dashboard'))
                        ->with('success', 'Welcome back, ' . $user->name . '!');
    }

    /**
     * Handle logout
     */
    public function logout(Request $request)
    {
        $user = Auth::user();

        if ($user) {
            AuditLog::logLogout($user);
        }

        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login')
                        ->with('success', 'You have been logged out successfully.');
    }

    /**
     * Show password change form
     */
    public function showPasswordChangeForm()
    {
        if (!session('password_change_required')) {
            return redirect()->route('login');
        }

        return view('auth.change-password');
    }

    /**
     * Handle password change
     */
    public function changePassword(Request $request)
    {
        if (!session('password_change_required')) {
            return redirect()->route('login');
        }

        $request->validate([
            'current_password' => 'required',
            'password' => [
                'required',
                'string',
                'min:' . config('security.authentication.password_min_length', 8),
                'confirmed',
            ],
        ]);

        $userId = session('user_id');
        $user = User::findOrFail($userId);

        // Verify current password
        if (!Hash::check($request->current_password, $user->password)) {
            throw ValidationException::withMessages([
                'current_password' => ['The current password is incorrect.'],
            ]);
        }

        // Update password
        $user->update([
            'password' => Hash::make($request->password),
            'password_changed_at' => now(),
        ]);

        // Log password change
        AuditLog::logEvent(
            event: 'password_changed',
            auditable: $user,
            severity: 'medium',
            description: 'User changed password due to expiry'
        );

        // Clear session flags
        session()->forget(['password_change_required', 'user_id']);

        // Log the user in
        Auth::login($user);
        $request->session()->regenerate();

        return redirect()->route('dashboard')
                        ->with('success', 'Password changed successfully. Welcome back!');
    }
}
