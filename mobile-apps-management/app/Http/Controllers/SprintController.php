<?php

namespace App\Http\Controllers;

use App\Models\Sprint;
use App\Models\AuditLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SprintController extends Controller
{
    /**
     * Display a listing of sprints
     */
    public function index()
    {
        if (!auth()->user()->can('sprints.read')) {
            abort(403, 'Unauthorized');
        }

        $sprints = Sprint::withCount('apps')
                        ->orderBy('created_at', 'desc')
                        ->paginate(15);

        return view('sprints.index', compact('sprints'));
    }

    /**
     * Show the form for creating a new sprint
     */
    public function create()
    {
        if (!auth()->user()->can('sprints.create')) {
            abort(403, 'Unauthorized');
        }

        return view('sprints.create');
    }

    /**
     * Store a newly created sprint
     */
    public function store(Request $request)
    {
        if (!auth()->user()->can('sprints.create')) {
            abort(403, 'Unauthorized');
        }

        $request->validate([
            'name' => 'required|string|max:255|unique:sprints,name',
            'description' => 'nullable|string|max:1000',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'required|in:planning,active,completed,cancelled'
        ]);

        try {
            DB::beginTransaction();

            $sprint = Sprint::create([
                'name' => $request->name,
                'description' => $request->description,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'status' => $request->status,
                'created_by' => auth()->id(),
            ]);

            // Log the creation
            AuditLog::logEvent(
                event: 'sprint_created',
                auditable: $sprint,
                newValues: [
                    'name' => $sprint->name,
                    'status' => $sprint->status,
                    'start_date' => $sprint->start_date,
                    'end_date' => $sprint->end_date,
                ],
                severity: 'low',
                description: "Sprint '{$sprint->name}' created"
            );

            DB::commit();

            return redirect()->route('sprints.index')
                           ->with('success', "Sprint '{$sprint->name}' created successfully!");

        } catch (\Exception $e) {
            DB::rollback();

            AuditLog::logEvent(
                event: 'sprint_creation_failed',
                severity: 'medium',
                description: 'Failed to create sprint',
                success: false,
                errorMessage: $e->getMessage()
            );

            return back()->withInput()
                        ->with('error', 'Failed to create sprint: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified sprint
     */
    public function show(Sprint $sprint)
    {
        if (!auth()->user()->can('sprints.read')) {
            abort(403, 'Unauthorized');
        }

        $sprint->load(['apps.uploader', 'creator']);
        
        return view('sprints.show', compact('sprint'));
    }

    /**
     * Show the form for editing the specified sprint
     */
    public function edit(Sprint $sprint)
    {
        if (!auth()->user()->can('sprints.update')) {
            abort(403, 'Unauthorized');
        }

        return view('sprints.edit', compact('sprint'));
    }

    /**
     * Update the specified sprint
     */
    public function update(Request $request, Sprint $sprint)
    {
        if (!auth()->user()->can('sprints.update')) {
            abort(403, 'Unauthorized');
        }

        $request->validate([
            'name' => 'required|string|max:255|unique:sprints,name,' . $sprint->id,
            'description' => 'nullable|string|max:1000',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'required|in:planning,active,completed,cancelled'
        ]);

        try {
            $oldValues = [
                'name' => $sprint->name,
                'description' => $sprint->description,
                'start_date' => $sprint->start_date,
                'end_date' => $sprint->end_date,
                'status' => $sprint->status,
            ];

            $sprint->update([
                'name' => $request->name,
                'description' => $request->description,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'status' => $request->status,
            ]);

            $newValues = [
                'name' => $sprint->name,
                'description' => $sprint->description,
                'start_date' => $sprint->start_date,
                'end_date' => $sprint->end_date,
                'status' => $sprint->status,
            ];

            // Log the update
            AuditLog::logEvent(
                event: 'sprint_updated',
                auditable: $sprint,
                oldValues: $oldValues,
                newValues: $newValues,
                severity: 'low',
                description: "Sprint '{$sprint->name}' updated"
            );

            return redirect()->route('sprints.show', $sprint)
                           ->with('success', "Sprint '{$sprint->name}' updated successfully!");

        } catch (\Exception $e) {
            AuditLog::logEvent(
                event: 'sprint_update_failed',
                auditable: $sprint,
                severity: 'medium',
                description: "Failed to update sprint '{$sprint->name}'",
                success: false,
                errorMessage: $e->getMessage()
            );

            return back()->withInput()
                        ->with('error', 'Failed to update sprint: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified sprint
     */
    public function destroy(Sprint $sprint)
    {
        if (!auth()->user()->can('sprints.delete')) {
            abort(403, 'Unauthorized');
        }

        try {
            // Check if sprint has apps
            if ($sprint->apps()->count() > 0) {
                return back()->with('error', 'Cannot delete sprint that has apps associated with it.');
            }

            $sprintName = $sprint->name;

            // Log the deletion
            AuditLog::logEvent(
                event: 'sprint_deleted',
                auditable: $sprint,
                oldValues: [
                    'name' => $sprint->name,
                    'status' => $sprint->status,
                ],
                severity: 'medium',
                description: "Sprint '{$sprintName}' deleted"
            );

            $sprint->delete();

            return redirect()->route('sprints.index')
                           ->with('success', "Sprint '{$sprintName}' deleted successfully!");

        } catch (\Exception $e) {
            AuditLog::logEvent(
                event: 'sprint_deletion_failed',
                auditable: $sprint,
                severity: 'high',
                description: "Failed to delete sprint '{$sprint->name}'",
                success: false,
                errorMessage: $e->getMessage()
            );

            return back()->with('error', 'Failed to delete sprint: ' . $e->getMessage());
        }
    }
}
