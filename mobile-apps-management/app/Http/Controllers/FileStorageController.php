<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Models\App;
use App\Models\FileAccessLog;
use App\Models\AuditLog;
use Carbon\Carbon;
use Symfony\Component\HttpFoundation\StreamedResponse;

class FileStorageController extends Controller
{
    /**
     * Generate secure download token for file access
     */
    public function generateDownloadToken(Request $request, App $app)
    {
        if (!auth()->user()->can('apps.download')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Validate app exists and user has access
        if (!$this->userCanAccessApp($app)) {
            AuditLog::logEvent(
                event: 'unauthorized_file_access_attempt',
                auditable: $app,
                severity: 'high',
                description: "Unauthorized attempt to generate download token for app '{$app->name}'"
            );
            return response()->json(['error' => 'Access denied'], 403);
        }

        // Check rate limiting
        if ($this->isRateLimited()) {
            return response()->json(['error' => 'Rate limit exceeded'], 429);
        }

        // Generate secure token
        $token = $this->createSecureToken($app);

        // Log token generation
        FileAccessLog::logAccess(
            app: $app,
            accessType: 'token_generated',
            success: true,
            downloadMethod: 'secure_token',
            metadata: [
                'token_expires_at' => now()->addSeconds(config('security.file_access.token_expiry', 3600)),
                'user_agent' => $request->userAgent(),
                'ip_address' => $request->ip(),
            ]
        );

        AuditLog::logEvent(
            event: 'download_token_generated',
            auditable: $app,
            severity: 'medium',
            description: "Download token generated for app '{$app->name}'"
        );

        return response()->json([
            'success' => true,
            'token' => $token,
            'expires_at' => now()->addSeconds(config('security.file_access.token_expiry', 3600)),
            'download_url' => route('files.download', ['app' => $app->id, 'token' => $token]),
        ]);
    }

    /**
     * Secure file download with token validation
     */
    public function secureDownload(Request $request, App $app, string $token)
    {
        try {
            // Validate token
            if (!$this->validateToken($token, $app)) {
                $this->logFailedAccess($request, $app, 'Invalid or expired token');
                return response()->json(['error' => 'Invalid or expired token'], 401);
            }

            // Additional security checks
            if (!$this->performSecurityChecks($request, $app)) {
                return response()->json(['error' => 'Security validation failed'], 403);
            }

            // Check file exists and is accessible
            if (!Storage::disk('private')->exists($app->file_path)) {
                $this->logFailedAccess($request, $app, 'File not found');
                return response()->json(['error' => 'File not found'], 404);
            }

            // Verify file integrity
            if (!$this->verifyFileIntegrity($app)) {
                $this->logFailedAccess($request, $app, 'File integrity check failed');
                return response()->json(['error' => 'File integrity error'], 500);
            }

            // Log successful access
            $this->logSuccessfulAccess($request, $app, $token);

            // Invalidate token after use (one-time use)
            $this->invalidateToken($token);

            // Stream file download
            return $this->streamFileDownload($app);

        } catch (\Exception $e) {
            Log::error('Secure download failed', [
                'app_id' => $app->id,
                'token' => $token,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->logFailedAccess($request, $app, 'Download failed: ' . $e->getMessage());
            return response()->json(['error' => 'Download failed'], 500);
        }
    }

    /**
     * Upload file with security validation
     */
    public function secureUpload(Request $request)
    {
        if (!auth()->user()->can('apps.upload')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $request->validate([
            'file' => [
                'required',
                'file',
                'max:' . $this->getMaxFileSizeInKb(),
                function ($attribute, $value, $fail) {
                    if (!$this->isAllowedFileType($value)) {
                        $fail('File type not allowed.');
                    }
                },
            ],
            'app_name' => 'required|string|max:255',
            'version' => 'required|string|max:50',
            'description' => 'nullable|string|max:1000',
        ]);

        DB::beginTransaction();
        try {
            $file = $request->file('file');

            // Security validation
            if (!$this->validateFileContent($file)) {
                throw new \Exception('File content validation failed');
            }

            // Generate secure file path
            $filePath = $this->generateSecureFilePath($file);

            // Store file in private storage
            $storedPath = Storage::disk('private')->putFileAs(
                dirname($filePath),
                $file,
                basename($filePath)
            );

            // Calculate file hash for integrity checking
            $fileHash = hash_file('sha256', $file->getRealPath());

            // Create app record
            $app = App::create([
                'name' => $request->app_name,
                'version' => $request->version,
                'description' => $request->description,
                'file_path' => $storedPath,
                'file_name' => $file->getClientOriginalName(),
                'file_size' => $file->getSize(),
                'file_hash' => $fileHash,
                'mime_type' => $file->getMimeType(),
                'uploaded_by' => auth()->id(),
                'platform' => $this->detectPlatform($file),
                'status' => 'quarantined', // Start in quarantine
                'quarantine_until' => now()->addSeconds(config('security.upload_security.quarantine_duration', 300)),
            ]);

            // Log upload
            AuditLog::logEvent(
                event: 'file_uploaded',
                auditable: $app,
                severity: 'medium',
                description: "File '{$app->name}' uploaded and quarantined",
                metadata: [
                    'file_size' => $app->file_size,
                    'file_hash' => $app->file_hash,
                    'platform' => $app->platform,
                ]
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully and placed in quarantine',
                'app_id' => $app->id,
                'quarantine_until' => $app->quarantine_until,
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            AuditLog::logEvent(
                event: 'file_upload_failed',
                severity: 'high',
                description: 'File upload failed: ' . $e->getMessage(),
                success: false,
                errorMessage: $e->getMessage()
            );

            return response()->json([
                'success' => false,
                'message' => 'Upload failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Release file from quarantine
     */
    public function releaseFromQuarantine(App $app)
    {
        if (!auth()->user()->can('apps.update')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        if ($app->status !== 'quarantined') {
            return response()->json(['error' => 'App is not in quarantine'], 422);
        }

        try {
            $app->update([
                'status' => 'active',
                'quarantine_until' => null,
                'released_by' => auth()->id(),
                'released_at' => now(),
            ]);

            AuditLog::logEvent(
                event: 'file_released_from_quarantine',
                auditable: $app,
                severity: 'medium',
                description: "App '{$app->name}' released from quarantine"
            );

            return response()->json([
                'success' => true,
                'message' => 'App released from quarantine successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to release from quarantine'
            ], 500);
        }
    }

    /**
     * Get file storage statistics
     */
    public function getStorageStats()
    {
        if (!auth()->user()->can('dashboard.read')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $stats = [
            'total_files' => App::count(),
            'total_size' => App::sum('file_size'),
            'quarantined_files' => App::where('status', 'quarantined')->count(),
            'active_files' => App::where('status', 'active')->count(),
            'total_downloads' => FileAccessLog::where('access_type', 'download')->where('success', true)->count(),
            'failed_downloads' => FileAccessLog::where('access_type', 'download')->where('success', false)->count(),
            'storage_usage_by_platform' => App::selectRaw('platform, COUNT(*) as count, SUM(file_size) as total_size')
                                             ->groupBy('platform')
                                             ->get(),
            'recent_uploads' => App::orderBy('created_at', 'desc')->limit(5)->get(),
        ];

        return response()->json($stats);
    }

    /**
     * Private helper methods
     */
    private function userCanAccessApp(App $app): bool
    {
        $user = auth()->user();

        // Super Admin and Admin can access all files
        if ($user->hasRole(['Super Admin', 'Admin'])) {
            return true;
        }

        // Users can access files they uploaded
        if ($app->uploaded_by === $user->id) {
            return true;
        }

        // Check if user has specific permission for this app
        return $user->can('apps.download');
    }

    private function isRateLimited(): bool
    {
        if (!config('security.file_access.enable_rate_limiting', true)) {
            return false;
        }

        $user = auth()->user();
        $key = "download_rate_limit:{$user->id}";
        $maxDownloads = config('security.file_access.max_downloads_per_hour', 50);

        $currentCount = Cache::get($key, 0);

        if ($currentCount >= $maxDownloads) {
            return true;
        }

        Cache::put($key, $currentCount + 1, 3600); // 1 hour
        return false;
    }

    private function createSecureToken(App $app): string
    {
        $payload = [
            'app_id' => $app->id,
            'user_id' => auth()->id(),
            'expires_at' => now()->addSeconds(config('security.file_access.token_expiry', 3600))->timestamp,
            'nonce' => Str::random(32),
        ];

        $token = base64_encode(json_encode($payload));
        $signature = hash_hmac('sha256', $token, config('app.key'));

        $secureToken = $token . '.' . $signature;

        // Store token in cache for validation
        Cache::put("download_token:{$secureToken}", $payload, config('security.file_access.token_expiry', 3600));

        return $secureToken;
    }

    private function validateToken(string $token, App $app): bool
    {
        try {
            // Check if token exists in cache
            $payload = Cache::get("download_token:{$token}");
            if (!$payload) {
                return false;
            }

            // Verify token structure
            $parts = explode('.', $token);
            if (count($parts) !== 2) {
                return false;
            }

            [$tokenData, $signature] = $parts;

            // Verify signature
            $expectedSignature = hash_hmac('sha256', $tokenData, config('app.key'));
            if (!hash_equals($expectedSignature, $signature)) {
                return false;
            }

            // Decode and validate payload
            $decodedPayload = json_decode(base64_decode($tokenData), true);
            if (!$decodedPayload) {
                return false;
            }

            // Check expiration
            if ($decodedPayload['expires_at'] < now()->timestamp) {
                Cache::forget("download_token:{$token}");
                return false;
            }

            // Check app ID matches
            if ($decodedPayload['app_id'] !== $app->id) {
                return false;
            }

            // Check user ID matches
            if ($decodedPayload['user_id'] !== auth()->id()) {
                return false;
            }

            return true;

        } catch (\Exception $e) {
            Log::error('Token validation failed', [
                'token' => $token,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    private function performSecurityChecks(Request $request, App $app): bool
    {
        // IP whitelist check
        if (config('security.file_access.require_ip_whitelist', false)) {
            $whitelist = explode(',', config('security.file_access.ip_whitelist', ''));
            $whitelist = array_map('trim', $whitelist);

            if (!empty($whitelist) && !in_array($request->ip(), $whitelist)) {
                $this->logFailedAccess($request, $app, 'IP not whitelisted');
                return false;
            }
        }

        // Check if IP is blocked
        if (Cache::has("blocked_ip:{$request->ip()}")) {
            $this->logFailedAccess($request, $app, 'IP address blocked');
            return false;
        }

        // Check app status
        if ($app->status !== 'active') {
            $this->logFailedAccess($request, $app, 'App not active');
            return false;
        }

        return true;
    }

    private function verifyFileIntegrity(App $app): bool
    {
        if (!Storage::disk('private')->exists($app->file_path)) {
            return false;
        }

        $filePath = Storage::disk('private')->path($app->file_path);
        $currentHash = hash_file('sha256', $filePath);

        return hash_equals($app->file_hash, $currentHash);
    }

    private function logSuccessfulAccess(Request $request, App $app, string $token): void
    {
        FileAccessLog::logAccess(
            app: $app,
            accessType: 'download',
            success: true,
            downloadMethod: 'secure_token',
            metadata: [
                'token_used' => substr($token, 0, 20) . '...',
                'file_size' => $app->file_size,
                'download_time' => now(),
            ]
        );

        AuditLog::logEvent(
            event: 'file_downloaded',
            auditable: $app,
            severity: 'low',
            description: "File '{$app->name}' downloaded successfully"
        );
    }

    private function logFailedAccess(Request $request, App $app, string $reason): void
    {
        FileAccessLog::logAccess(
            app: $app,
            accessType: 'download',
            success: false,
            errorMessage: $reason,
            downloadMethod: 'secure_token'
        );

        AuditLog::logEvent(
            event: 'file_download_failed',
            auditable: $app,
            severity: 'medium',
            description: "Failed download attempt for '{$app->name}': {$reason}",
            success: false,
            errorMessage: $reason
        );

        // Increment failed attempts for IP
        $this->incrementFailedAttempts($request->ip());
    }

    private function incrementFailedAttempts(string $ip): void
    {
        $key = "failed_download_attempts:{$ip}";
        $attempts = Cache::increment($key, 1);

        if ($attempts === 1) {
            Cache::put($key, 1, 3600); // 1 hour expiry
        }

        $maxAttempts = config('security.file_access.max_failed_attempts', 10);
        if ($attempts >= $maxAttempts) {
            $blockDuration = config('security.file_access.ip_block_duration', 60);
            Cache::put("blocked_ip:{$ip}", true, $blockDuration * 60);

            AuditLog::logEvent(
                event: 'ip_blocked_download_attempts',
                severity: 'high',
                description: "IP {$ip} blocked due to excessive failed download attempts",
                metadata: ['failed_attempts' => $attempts, 'block_duration' => $blockDuration]
            );
        }
    }

    private function invalidateToken(string $token): void
    {
        Cache::forget("download_token:{$token}");
    }

    private function streamFileDownload(App $app): StreamedResponse
    {
        $filePath = Storage::disk('private')->path($app->file_path);

        return response()->streamDownload(function () use ($filePath) {
            $stream = fopen($filePath, 'r');
            fpassthru($stream);
            fclose($stream);
        }, $app->file_name, [
            'Content-Type' => $app->mime_type,
            'Content-Length' => $app->file_size,
            'Content-Disposition' => 'attachment; filename="' . $app->file_name . '"',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }

    private function getMaxFileSizeInKb(): int
    {
        $maxSize = config('security.upload_security.max_file_size', '500MB');

        // Convert to KB
        if (str_contains($maxSize, 'GB')) {
            return (int) str_replace('GB', '', $maxSize) * 1024 * 1024;
        } elseif (str_contains($maxSize, 'MB')) {
            return (int) str_replace('MB', '', $maxSize) * 1024;
        } elseif (str_contains($maxSize, 'KB')) {
            return (int) str_replace('KB', '', $maxSize);
        }

        return 512000; // 500MB default
    }

    private function isAllowedFileType($file): bool
    {
        $allowedExtensions = config('security.upload_security.allowed_extensions', ['ipa', 'apk']);
        $extension = strtolower($file->getClientOriginalExtension());

        return in_array($extension, $allowedExtensions);
    }

    private function validateFileContent($file): bool
    {
        if (!config('security.upload_security.validate_file_content', true)) {
            return true;
        }

        $extension = strtolower($file->getClientOriginalExtension());
        $mimeType = $file->getMimeType();

        // Validate APK files
        if ($extension === 'apk') {
            return $this->validateApkFile($file);
        }

        // Validate IPA files
        if ($extension === 'ipa') {
            return $this->validateIpaFile($file);
        }

        return false;
    }

    private function validateApkFile($file): bool
    {
        // Basic APK validation - check for ZIP structure and AndroidManifest.xml
        try {
            $zip = new \ZipArchive();
            if ($zip->open($file->getRealPath()) === true) {
                $hasManifest = $zip->locateName('AndroidManifest.xml') !== false;
                $zip->close();
                return $hasManifest;
            }
        } catch (\Exception $e) {
            Log::warning('APK validation failed', ['error' => $e->getMessage()]);
        }

        return false;
    }

    private function validateIpaFile($file): bool
    {
        // Basic IPA validation - check for ZIP structure and Payload directory
        try {
            $zip = new \ZipArchive();
            if ($zip->open($file->getRealPath()) === true) {
                $hasPayload = false;
                for ($i = 0; $i < $zip->numFiles; $i++) {
                    $filename = $zip->getNameIndex($i);
                    if (str_starts_with($filename, 'Payload/')) {
                        $hasPayload = true;
                        break;
                    }
                }
                $zip->close();
                return $hasPayload;
            }
        } catch (\Exception $e) {
            Log::warning('IPA validation failed', ['error' => $e->getMessage()]);
        }

        return false;
    }

    private function generateSecureFilePath($file): string
    {
        $extension = $file->getClientOriginalExtension();
        $hash = hash('sha256', $file->getClientOriginalName() . time() . auth()->id());
        $directory = 'apps/' . date('Y/m');

        return $directory . '/' . $hash . '.' . $extension;
    }

    private function detectPlatform($file): string
    {
        $extension = strtolower($file->getClientOriginalExtension());

        return match ($extension) {
            'apk' => 'android',
            'ipa' => 'ios',
            default => 'unknown'
        };
    }
}
