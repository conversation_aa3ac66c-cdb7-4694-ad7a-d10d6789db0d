<?php

namespace App\Http\Controllers;

use App\Models\AuditLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AuditLogController extends Controller
{
    /**
     * Display a listing of audit logs
     */
    public function index(Request $request)
    {
        if (!auth()->user()->can('audit_logs.view')) {
            abort(403, 'Unauthorized');
        }

        $query = AuditLog::with(['user']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('event', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('user_email', 'like', "%{$search}%")
                  ->orWhere('ip_address', 'like', "%{$search}%");
            });
        }

        // Filter by event type
        if ($request->filled('event')) {
            $query->where('event', $request->get('event'));
        }

        // Filter by severity
        if ($request->filled('severity')) {
            $query->where('severity', $request->get('severity'));
        }

        // Filter by user
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->get('user_id'));
        }

        // Filter by success status
        if ($request->filled('success')) {
            $success = $request->get('success');
            if ($success === 'true') {
                $query->where('success', true);
            } elseif ($success === 'false') {
                $query->where('success', false);
            }
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');

        if (in_array($sortBy, ['created_at', 'event', 'severity', 'user_email', 'success'])) {
            $query->orderBy($sortBy, $sortDirection);
        }

        $auditLogs = $query->paginate(25)->withQueryString();

        // Get filter options
        $events = AuditLog::distinct()->pluck('event')->sort()->values();
        $severities = ['low', 'medium', 'high', 'critical'];
        $users = User::orderBy('name')->get(['id', 'name', 'email']);

        // Get statistics
        $stats = [
            'total_logs' => AuditLog::count(),
            'today_logs' => AuditLog::whereDate('created_at', today())->count(),
            'failed_actions' => AuditLog::where('success', false)->count(),
            'unique_users' => AuditLog::distinct('user_id')->count('user_id'),
            'severity_breakdown' => AuditLog::selectRaw('severity, COUNT(*) as count')
                                          ->groupBy('severity')
                                          ->pluck('count', 'severity')
                                          ->toArray(),
        ];

        return view('audit-logs.index', compact('auditLogs', 'events', 'severities', 'users', 'stats'));
    }

    /**
     * Display the specified audit log
     */
    public function show(AuditLog $auditLog)
    {
        if (!auth()->user()->can('audit_logs.view')) {
            abort(403, 'Unauthorized');
        }

        $auditLog->load(['user', 'auditable']);

        return view('audit-logs.show', compact('auditLog'));
    }

    /**
     * Export audit logs
     */
    public function export(Request $request)
    {
        if (!auth()->user()->can('audit_logs.view')) {
            abort(403, 'Unauthorized');
        }

        $query = AuditLog::with(['user']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('event', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('user_email', 'like', "%{$search}%")
                  ->orWhere('ip_address', 'like', "%{$search}%");
            });
        }

        if ($request->filled('event')) {
            $query->where('event', $request->get('event'));
        }

        if ($request->filled('severity')) {
            $query->where('severity', $request->get('severity'));
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->get('user_id'));
        }

        if ($request->filled('success')) {
            $success = $request->get('success');
            if ($success === 'true') {
                $query->where('success', true);
            } elseif ($success === 'false') {
                $query->where('success', false);
            }
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        $auditLogs = $query->orderBy('created_at', 'desc')->limit(10000)->get();

        // Generate CSV
        $filename = 'audit_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($auditLogs) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'ID',
                'Event',
                'User',
                'Email',
                'IP Address',
                'Method',
                'URL',
                'Severity',
                'Success',
                'Description',
                'Created At'
            ]);

            // CSV data
            foreach ($auditLogs as $log) {
                fputcsv($file, [
                    $log->id,
                    $log->event,
                    $log->user?->name ?? 'System',
                    $log->user_email ?? 'N/A',
                    $log->ip_address,
                    $log->method,
                    $log->url,
                    $log->severity,
                    $log->success ? 'Yes' : 'No',
                    $log->description,
                    $log->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get audit log statistics for dashboard
     */
    public function stats()
    {
        if (!auth()->user()->can('audit_logs.view')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $stats = [
            'total_logs' => AuditLog::count(),
            'today_logs' => AuditLog::whereDate('created_at', today())->count(),
            'this_week_logs' => AuditLog::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'failed_actions' => AuditLog::where('success', false)->count(),
            'unique_users_today' => AuditLog::whereDate('created_at', today())->distinct('user_id')->count('user_id'),
            'top_events' => AuditLog::selectRaw('event, COUNT(*) as count')
                                  ->whereDate('created_at', today())
                                  ->groupBy('event')
                                  ->orderBy('count', 'desc')
                                  ->limit(5)
                                  ->get(),
            'severity_breakdown' => AuditLog::selectRaw('severity, COUNT(*) as count')
                                          ->whereDate('created_at', today())
                                          ->groupBy('severity')
                                          ->pluck('count', 'severity')
                                          ->toArray(),
        ];

        return response()->json($stats);
    }
}
