<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\AuditLog;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class UserController extends Controller
{
    /**
     * Display a listing of users
     */
    public function index(Request $request)
    {
        $query = User::with(['roles']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->role($request->get('role'));
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->get('status') === 'active') {
                $query->where('is_active', true);
            } elseif ($request->get('status') === 'inactive') {
                $query->where('is_active', false);
            } elseif ($request->get('status') === 'locked') {
                $query->whereNotNull('locked_until')
                      ->where('locked_until', '>', now());
            }
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');

        if (in_array($sortBy, ['name', 'email', 'created_at', 'last_login_at'])) {
            $query->orderBy($sortBy, $sortDirection);
        }

        $users = $query->paginate(15)->withQueryString();
        $roles = Role::all();

        // Log the view action
        AuditLog::logEvent(
            event: 'users_viewed',
            severity: 'low',
            description: 'User viewed users listing'
        );

        return view('users.index', compact('users', 'roles'));
    }

    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        $roles = Role::all();
        return view('users.create', compact('roles'));
    }

    /**
     * Store a newly created user
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => [
                'required',
                'confirmed',
                Password::min(config('security.authentication.password_min_length', 8))
                    ->when(config('security.authentication.require_password_complexity', true), function ($rule) {
                        return $rule->mixedCase()->numbers()->symbols();
                    })
            ],
            'roles' => 'array',
            'roles.*' => 'exists:roles,name',
            'is_active' => 'boolean',
        ]);

        DB::beginTransaction();
        try {
            // Create the user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'is_active' => $request->boolean('is_active', true),
                'email_verified_at' => now(),
                'password_changed_at' => now(),
            ]);

            // Assign roles
            if ($request->filled('roles')) {
                $user->assignRole($request->roles);
            }

            // Log the creation
            AuditLog::logEvent(
                event: 'user_created',
                auditable: $user,
                newValues: [
                    'name' => $user->name,
                    'email' => $user->email,
                    'roles' => $request->roles ?? [],
                    'is_active' => $user->is_active,
                ],
                severity: 'medium',
                description: "User '{$user->name}' created with " . count($request->roles ?? []) . " roles"
            );

            DB::commit();

            return redirect()->route('users.index')
                           ->with('success', "User '{$user->name}' created successfully!");

        } catch (\Exception $e) {
            DB::rollback();

            AuditLog::logEvent(
                event: 'user_creation_failed',
                severity: 'high',
                description: 'Failed to create user',
                success: false,
                errorMessage: $e->getMessage()
            );

            return back()->withInput()
                        ->with('error', 'Failed to create user. Please try again.');
        }
    }

    /**
     * Display the specified user
     */
    public function show(User $user)
    {
        $user->load(['roles', 'uploadedApps', 'createdSprints']);

        // Get recent activity
        $recentActivity = AuditLog::where('user_id', $user->id)
                                 ->orderBy('created_at', 'desc')
                                 ->limit(10)
                                 ->get();

        // Get user statistics
        $stats = [
            'apps_uploaded' => $user->uploadedApps()->count(),
            'sprints_created' => $user->createdSprints()->count(),
            'last_login' => $user->last_login_at,
            'total_logins' => AuditLog::where('user_id', $user->id)
                                    ->where('event', 'login')
                                    ->count(),
        ];

        AuditLog::logEvent(
            event: 'user_viewed',
            auditable: $user,
            severity: 'low',
            description: "User '{$user->name}' profile viewed"
        );

        return view('users.show', compact('user', 'recentActivity', 'stats'));
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        $roles = Role::all();
        $userRoles = $user->roles->pluck('name')->toArray();

        return view('users.edit', compact('user', 'roles', 'userRoles'));
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users', 'email')->ignore($user->id),
            ],
            'password' => [
                'nullable',
                'confirmed',
                Password::min(config('security.authentication.password_min_length', 8))
                    ->when(config('security.authentication.require_password_complexity', true), function ($rule) {
                        return $rule->mixedCase()->numbers()->symbols();
                    })
            ],
            'roles' => 'array',
            'roles.*' => 'exists:roles,name',
            'is_active' => 'boolean',
        ]);

        DB::beginTransaction();
        try {
            $oldValues = [
                'name' => $user->name,
                'email' => $user->email,
                'roles' => $user->roles->pluck('name')->toArray(),
                'is_active' => $user->is_active,
            ];

            // Update user data
            $updateData = [
                'name' => $request->name,
                'email' => $request->email,
                'is_active' => $request->boolean('is_active', true),
            ];

            // Update password if provided
            if ($request->filled('password')) {
                $updateData['password'] = Hash::make($request->password);
                $updateData['password_changed_at'] = now();

                // Reset failed login attempts and unlock account
                $updateData['failed_login_attempts'] = 0;
                $updateData['locked_until'] = null;
            }

            $user->update($updateData);

            // Sync roles
            $user->syncRoles($request->roles ?? []);

            $newValues = [
                'name' => $user->name,
                'email' => $user->email,
                'roles' => $request->roles ?? [],
                'is_active' => $user->is_active,
            ];

            // Log the update
            AuditLog::logEvent(
                event: 'user_updated',
                auditable: $user,
                oldValues: $oldValues,
                newValues: $newValues,
                severity: 'medium',
                description: "User '{$user->name}' updated"
            );

            DB::commit();

            return redirect()->route('users.index')
                           ->with('success', "User '{$user->name}' updated successfully!");

        } catch (\Exception $e) {
            DB::rollback();

            AuditLog::logEvent(
                event: 'user_update_failed',
                auditable: $user,
                severity: 'high',
                description: "Failed to update user '{$user->name}'",
                success: false,
                errorMessage: $e->getMessage()
            );

            return back()->withInput()
                        ->with('error', 'Failed to update user. Please try again.');
        }
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user)
    {
        // Prevent deleting current user
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot delete your own account.'
            ], 403);
        }

        // Prevent deleting the last Super Admin
        if ($user->hasRole('Super Admin')) {
            $superAdminCount = User::role('Super Admin')->count();
            if ($superAdminCount <= 1) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete the last Super Admin user.'
                ], 422);
            }
        }

        DB::beginTransaction();
        try {
            $userName = $user->name;
            $userEmail = $user->email;
            $userRoles = $user->roles->pluck('name')->toArray();

            // Delete the user
            $user->delete();

            // Log the deletion
            AuditLog::logEvent(
                event: 'user_deleted',
                auditable: null,
                oldValues: [
                    'name' => $userName,
                    'email' => $userEmail,
                    'roles' => $userRoles,
                ],
                severity: 'high',
                description: "User '{$userName}' deleted"
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "User '{$userName}' deleted successfully!"
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            AuditLog::logEvent(
                event: 'user_deletion_failed',
                auditable: $user,
                severity: 'high',
                description: "Failed to delete user '{$user->name}'",
                success: false,
                errorMessage: $e->getMessage()
            );

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete user. Please try again.'
            ], 500);
        }
    }

    /**
     * Toggle user active status
     */
    public function toggleStatus(User $user)
    {
        if (!auth()->user()->can('users.update')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Prevent deactivating current user
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot deactivate your own account.'
            ], 403);
        }

        // Prevent deactivating the last Super Admin
        if ($user->hasRole('Super Admin') && $user->is_active) {
            $activeSuperAdmins = User::role('Super Admin')->where('is_active', true)->count();
            if ($activeSuperAdmins <= 1) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot deactivate the last active Super Admin.'
                ], 422);
            }
        }

        try {
            $oldStatus = $user->is_active;
            $user->update(['is_active' => !$user->is_active]);
            $newStatus = $user->is_active;

            AuditLog::logEvent(
                event: 'user_status_changed',
                auditable: $user,
                oldValues: ['is_active' => $oldStatus],
                newValues: ['is_active' => $newStatus],
                severity: 'medium',
                description: "User '{$user->name}' " . ($newStatus ? 'activated' : 'deactivated')
            );

            return response()->json([
                'success' => true,
                'message' => "User '{$user->name}' " . ($newStatus ? 'activated' : 'deactivated') . " successfully!",
                'new_status' => $newStatus
            ]);

        } catch (\Exception $e) {
            AuditLog::logEvent(
                event: 'user_status_change_failed',
                auditable: $user,
                severity: 'high',
                description: "Failed to change status for user '{$user->name}'",
                success: false,
                errorMessage: $e->getMessage()
            );

            return response()->json([
                'success' => false,
                'message' => 'Failed to change user status. Please try again.'
            ], 500);
        }
    }

    /**
     * Unlock user account
     */
    public function unlock(User $user)
    {
        if (!auth()->user()->can('users.update')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $user->update([
                'locked_until' => null,
                'failed_login_attempts' => 0,
            ]);

            AuditLog::logEvent(
                event: 'user_unlocked',
                auditable: $user,
                severity: 'medium',
                description: "User '{$user->name}' account unlocked"
            );

            return response()->json([
                'success' => true,
                'message' => "User '{$user->name}' account unlocked successfully!"
            ]);

        } catch (\Exception $e) {
            AuditLog::logEvent(
                event: 'user_unlock_failed',
                auditable: $user,
                severity: 'high',
                description: "Failed to unlock user '{$user->name}'",
                success: false,
                errorMessage: $e->getMessage()
            );

            return response()->json([
                'success' => false,
                'message' => 'Failed to unlock user account. Please try again.'
            ], 500);
        }
    }

    /**
     * Force password reset for user
     */
    public function forcePasswordReset(User $user)
    {
        if (!auth()->user()->can('users.update')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $user->update([
                'password_changed_at' => now()->subDays(config('security.authentication.force_password_change_days', 90) + 1),
            ]);

            AuditLog::logEvent(
                event: 'password_reset_forced',
                auditable: $user,
                severity: 'high',
                description: "Password reset forced for user '{$user->name}'"
            );

            return response()->json([
                'success' => true,
                'message' => "Password reset forced for '{$user->name}'. They will be required to change their password on next login."
            ]);

        } catch (\Exception $e) {
            AuditLog::logEvent(
                event: 'password_reset_force_failed',
                auditable: $user,
                severity: 'high',
                description: "Failed to force password reset for user '{$user->name}'",
                success: false,
                errorMessage: $e->getMessage()
            );

            return response()->json([
                'success' => false,
                'message' => 'Failed to force password reset. Please try again.'
            ], 500);
        }
    }

    /**
     * Get user activity for AJAX requests
     */
    public function activity(User $user)
    {
        if (!auth()->user()->can('users.read')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $activity = AuditLog::where('user_id', $user->id)
                           ->orderBy('created_at', 'desc')
                           ->limit(20)
                           ->get()
                           ->map(function ($log) {
                               return [
                                   'event' => $log->event,
                                   'description' => $log->description,
                                   'severity' => $log->severity,
                                   'created_at' => $log->created_at->format('M d, Y H:i'),
                                   'success' => $log->success,
                               ];
                           });

        return response()->json(['activity' => $activity]);
    }

    /**
     * Bulk operations on users
     */
    public function bulkAction(Request $request)
    {
        if (!auth()->user()->can('users.update')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $userIds = $request->user_ids;
        $action = $request->action;

        // Remove current user from bulk operations
        $userIds = array_filter($userIds, function ($id) {
            return $id != auth()->id();
        });

        if (empty($userIds)) {
            return response()->json([
                'success' => false,
                'message' => 'No valid users selected for bulk operation.'
            ], 422);
        }

        DB::beginTransaction();
        try {
            $users = User::whereIn('id', $userIds)->get();
            $processedCount = 0;

            foreach ($users as $user) {
                switch ($action) {
                    case 'activate':
                        if (!$user->is_active) {
                            $user->update(['is_active' => true]);
                            $processedCount++;
                        }
                        break;
                    case 'deactivate':
                        // Skip Super Admins in bulk deactivation
                        if (!$user->hasRole('Super Admin') && $user->is_active) {
                            $user->update(['is_active' => false]);
                            $processedCount++;
                        }
                        break;
                    case 'delete':
                        // Skip Super Admins in bulk deletion
                        if (!$user->hasRole('Super Admin')) {
                            $user->delete();
                            $processedCount++;
                        }
                        break;
                }
            }

            AuditLog::logEvent(
                event: 'bulk_user_action',
                severity: 'high',
                description: "Bulk {$action} performed on {$processedCount} users",
                metadata: [
                    'action' => $action,
                    'user_count' => $processedCount,
                    'user_ids' => $userIds,
                ]
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "Bulk {$action} completed successfully. {$processedCount} users processed."
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            AuditLog::logEvent(
                event: 'bulk_user_action_failed',
                severity: 'high',
                description: "Failed to perform bulk {$action}",
                success: false,
                errorMessage: $e->getMessage()
            );

            return response()->json([
                'success' => false,
                'message' => 'Failed to perform bulk operation. Please try again.'
            ], 500);
        }
    }
}
