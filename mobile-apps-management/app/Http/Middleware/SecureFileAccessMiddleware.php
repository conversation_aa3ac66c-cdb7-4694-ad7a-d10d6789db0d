<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\App;
use App\Models\FileAccessLog;
use App\Models\AuditLog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;

class SecureFileAccessMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Ensure user is authenticated
        if (!Auth::check()) {
            $this->logFailedAccess($request, 'Unauthenticated access attempt');
            return response()->json(['error' => 'Authentication required'], 401);
        }

        // Check if user account is active and not locked
        $user = Auth::user();
        if (!$user->is_active || $user->isLocked()) {
            $this->logFailedAccess($request, 'Inactive or locked user account');
            return response()->json(['error' => 'Account access denied'], 403);
        }

        // Rate limiting
        if ($this->isRateLimited($request)) {
            $this->logFailedAccess($request, 'Rate limit exceeded');
            return response()->json(['error' => 'Too many requests'], 429);
        }

        // Validate access token
        $app = $this->validateAccessToken($request);
        if (!$app) {
            $this->logFailedAccess($request, 'Invalid or expired access token');
            return response()->json(['error' => 'Invalid access token'], 403);
        }

        // Check user permissions
        if (!$this->hasFileAccessPermission($user, $app)) {
            $this->logFailedAccess($request, 'Insufficient permissions', $app);
            return response()->json(['error' => 'Insufficient permissions'], 403);
        }

        // IP whitelist check (if enabled)
        if ($this->isIpBlocked($request)) {
            $this->logFailedAccess($request, 'IP address not whitelisted', $app);
            return response()->json(['error' => 'Access denied from this IP'], 403);
        }

        // Check file integrity
        if (!$this->verifyFileIntegrity($app)) {
            $this->logFailedAccess($request, 'File integrity check failed', $app);
            return response()->json(['error' => 'File integrity error'], 500);
        }

        // Log successful access
        $this->logSuccessfulAccess($request, $app);

        // Add app to request for controller use
        $request->merge(['validated_app' => $app]);

        return $next($request);
    }

    /**
     * Check if request is rate limited
     */
    private function isRateLimited(Request $request): bool
    {
        if (!config('security.file_access.enable_rate_limiting', true)) {
            return false;
        }

        $maxAttempts = config('security.file_access.max_downloads_per_hour', 50);
        $key = 'file_access:' . $request->ip() . ':' . Auth::id();

        return RateLimiter::tooManyAttempts($key, $maxAttempts);
    }

    /**
     * Validate access token and return app
     */
    private function validateAccessToken(Request $request): ?App
    {
        $token = $request->get('token');
        $appId = $request->route('app');

        if (!$token || !$appId) {
            return null;
        }

        $app = App::where('id', $appId)
                  ->where('access_token', $token)
                  ->first();

        if (!$app) {
            return null;
        }

        // Check if token is expired (if token expiry is implemented)
        $tokenExpiry = config('security.file_access.token_expiry', 3600);
        if ($tokenExpiry > 0) {
            $tokenAge = now()->diffInSeconds($app->updated_at);
            if ($tokenAge > $tokenExpiry) {
                return null;
            }
        }

        return $app;
    }

    /**
     * Check if user has permission to access the file
     */
    private function hasFileAccessPermission($user, App $app): bool
    {
        // Check if user has general file download permission
        if (!$user->can('apps.download')) {
            return false;
        }

        // Additional checks can be added here
        // For example, check if user belongs to the same organization as the app uploader

        return true;
    }

    /**
     * Check if IP is blocked or not whitelisted
     */
    private function isIpBlocked(Request $request): bool
    {
        $ip = $request->ip();

        // Check if IP is in blocked list
        $blockedKey = "blocked_ip:{$ip}";
        if (Cache::has($blockedKey)) {
            return true;
        }

        // Check IP whitelist (if enabled)
        if (config('security.file_access.require_ip_whitelist', false)) {
            $whitelist = explode(',', config('security.file_access.ip_whitelist', ''));
            $whitelist = array_map('trim', $whitelist);

            if (!empty($whitelist) && !in_array($ip, $whitelist)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Verify file integrity using stored hash
     */
    private function verifyFileIntegrity(App $app): bool
    {
        $filePath = storage_path('app/' . $app->file_path);

        if (!file_exists($filePath)) {
            return false;
        }

        $currentHash = hash_file('sha256', $filePath);
        return $currentHash === $app->file_hash;
    }

    /**
     * Log failed access attempt
     */
    private function logFailedAccess(Request $request, string $reason, ?App $app = null): void
    {
        // Log to file access logs
        FileAccessLog::create([
            'app_id' => $app?->id,
            'user_id' => Auth::id(),
            'user_email' => Auth::user()?->email,
            'access_type' => 'download',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'file_path' => $app?->file_path ?? 'unknown',
            'access_token' => $request->get('token'),
            'success' => false,
            'error_message' => $reason,
            'download_method' => 'direct',
        ]);

        // Log to audit logs
        AuditLog::logEvent(
            event: 'file_access_denied',
            auditable: $app,
            severity: 'high',
            description: "File access denied: {$reason}",
            success: false,
            errorMessage: $reason
        );

        // Increment failed attempts for IP blocking
        $this->incrementFailedAttempts($request->ip());
    }

    /**
     * Log successful access
     */
    private function logSuccessfulAccess(Request $request, App $app): void
    {
        // Log to file access logs
        FileAccessLog::logAccess(
            app: $app,
            accessType: 'download',
            success: true,
            downloadMethod: 'direct'
        );

        // Log to audit logs
        AuditLog::logFileAccess($app, 'download', true);

        // Increment download counter
        $app->incrementDownloadCount();

        // Hit rate limiter
        if (config('security.file_access.enable_rate_limiting', true)) {
            $key = 'file_access:' . $request->ip() . ':' . Auth::id();
            RateLimiter::hit($key, 3600); // 1 hour window
        }
    }

    /**
     * Increment failed attempts for IP
     */
    private function incrementFailedAttempts(string $ip): void
    {
        $key = "failed_attempts:{$ip}";
        $attempts = Cache::increment($key, 1);

        if ($attempts === 1) {
            Cache::put($key, 1, 3600); // 1 hour expiry
        }

        $maxAttempts = config('security.file_access.max_failed_attempts', 10);
        if ($attempts >= $maxAttempts) {
            $blockDuration = config('security.file_access.ip_block_duration', 60);
            Cache::put("blocked_ip:{$ip}", true, $blockDuration * 60); // Convert to seconds
        }
    }
}
