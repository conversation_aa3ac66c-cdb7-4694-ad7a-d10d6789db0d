<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\AuditLog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AuditLogMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);

        // Process the request
        $response = $next($request);

        // Log the request after processing
        $this->logRequest($request, $response, $startTime);

        return $response;
    }

    /**
     * Log the request details
     */
    private function logRequest(Request $request, Response $response, float $startTime): void
    {
        // Skip logging for certain routes to avoid noise
        if ($this->shouldSkipLogging($request)) {
            return;
        }

        $user = Auth::user();
        $processingTime = round((microtime(true) - $startTime) * 1000, 2); // in milliseconds

        // Determine event type based on HTTP method and route
        $event = $this->determineEventType($request);

        // Determine severity based on response status and event type
        $severity = $this->determineSeverity($response->getStatusCode(), $event);

        // Prepare metadata
        $metadata = [
            'processing_time_ms' => $processingTime,
            'response_status' => $response->getStatusCode(),
            'request_size' => strlen($request->getContent()),
            'response_size' => strlen($response->getContent()),
        ];

        // Add route parameters if available
        if ($request->route()) {
            $metadata['route_name'] = $request->route()->getName();
            $metadata['route_parameters'] = $request->route()->parameters();
        }

        // Log the audit entry
        try {
            AuditLog::create([
                'event' => $event,
                'auditable_type' => null,
                'auditable_id' => null,
                'user_id' => $user?->id,
                'user_email' => $user?->email,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'old_values' => null,
                'new_values' => null,
                'metadata' => $metadata,
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'severity' => $severity,
                'description' => $this->generateDescription($request, $response),
                'success' => $response->getStatusCode() < 400,
                'error_message' => $response->getStatusCode() >= 400 ? 'HTTP ' . $response->getStatusCode() : null,
            ]);
        } catch (\Exception $e) {
            // Log error but don't break the application
            Log::error('Audit log creation failed: ' . $e->getMessage());
        }
    }

    /**
     * Determine if we should skip logging for this request
     */
    private function shouldSkipLogging(Request $request): bool
    {
        $skipRoutes = [
            'telescope*',
            'horizon*',
            '_debugbar*',
            'livewire*',
        ];

        $skipPaths = [
            '/css/',
            '/js/',
            '/images/',
            '/fonts/',
            '/favicon.ico',
        ];

        // Skip asset requests
        foreach ($skipPaths as $path) {
            if (str_contains($request->getPathInfo(), $path)) {
                return true;
            }
        }

        // Skip certain routes
        foreach ($skipRoutes as $route) {
            if ($request->is($route)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Determine event type based on request
     */
    private function determineEventType(Request $request): string
    {
        $method = $request->method();
        $path = $request->getPathInfo();

        // Authentication events
        if (str_contains($path, '/login')) {
            return $method === 'POST' ? 'login_attempt' : 'login_page_view';
        }

        if (str_contains($path, '/logout')) {
            return 'logout';
        }

        // File access events
        if (str_contains($path, '/download') || str_contains($path, '/file')) {
            return 'file_access';
        }

        // CRUD operations
        return match($method) {
            'GET' => 'view',
            'POST' => 'create',
            'PUT', 'PATCH' => 'update',
            'DELETE' => 'delete',
            default => 'request'
        };
    }

    /**
     * Determine severity based on response status and event type
     */
    private function determineSeverity(int $statusCode, string $event): string
    {
        // Critical events
        if (in_array($event, ['login_attempt', 'file_access']) && $statusCode >= 400) {
            return 'high';
        }

        // Error responses
        if ($statusCode >= 500) {
            return 'critical';
        }

        if ($statusCode >= 400) {
            return 'medium';
        }

        // Success responses
        if (in_array($event, ['create', 'update', 'delete'])) {
            return 'medium';
        }

        return 'low';
    }

    /**
     * Generate description for the audit log
     */
    private function generateDescription(Request $request, Response $response): string
    {
        $method = $request->method();
        $path = $request->getPathInfo();
        $status = $response->getStatusCode();

        return "{$method} {$path} - HTTP {$status}";
    }
}
