<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\FileStorageController;
use App\Http\Controllers\AppController;
use App\Http\Controllers\SprintController;
use App\Http\Controllers\AuditLogController;

// Redirect root to login
Route::get('/', function () {
    return redirect()->route('login');
});

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [LoginController::class, 'login']);
    Route::get('/password/change', [LoginController::class, 'showPasswordChangeForm'])->name('password.change.form');
    Route::post('/password/change', [LoginController::class, 'changePassword'])->name('password.change');
});

Route::middleware('auth')->group(function () {
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

    // Dashboard
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    // User Management (Order matters - specific routes before parameterized ones)
    Route::middleware('permission:users.read')->group(function () {
        Route::get('users', [UserController::class, 'index'])->name('users.index');
    });

    Route::middleware('permission:users.create')->group(function () {
        Route::get('users/create', [UserController::class, 'create'])->name('users.create');
        Route::post('users', [UserController::class, 'store'])->name('users.store');
    });

    Route::middleware('permission:users.read')->group(function () {
        Route::get('users/{user}', [UserController::class, 'show'])->name('users.show');
        Route::get('users/{user}/activity', [UserController::class, 'activity'])->name('users.activity');
    });

    Route::middleware('permission:users.update')->group(function () {
        Route::get('users/{user}/edit', [UserController::class, 'edit'])->name('users.edit');
        Route::put('users/{user}', [UserController::class, 'update'])->name('users.update');
        Route::post('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');
        Route::post('users/{user}/unlock', [UserController::class, 'unlock'])->name('users.unlock');
        Route::post('users/{user}/force-password-reset', [UserController::class, 'forcePasswordReset'])->name('users.force-password-reset');
        Route::post('users/bulk-action', [UserController::class, 'bulkAction'])->name('users.bulk-action');
    });

    Route::middleware('permission:users.delete')->group(function () {
        Route::delete('users/{user}', [UserController::class, 'destroy'])->name('users.destroy');
    });

    // Role & Permission Management (Order matters - specific routes before parameterized ones)
    Route::middleware('permission:roles.read')->group(function () {
        Route::get('roles', [RoleController::class, 'index'])->name('roles.index');
    });

    Route::middleware('permission:roles.create')->group(function () {
        Route::get('roles/create', [RoleController::class, 'create'])->name('roles.create');
        Route::post('roles', [RoleController::class, 'store'])->name('roles.store');
    });

    Route::middleware('permission:roles.read')->group(function () {
        Route::get('roles/{role}', [RoleController::class, 'show'])->name('roles.show');
        Route::get('roles/{role}/permissions', [RoleController::class, 'permissions'])->name('roles.permissions');
    });

    Route::middleware('permission:roles.update')->group(function () {
        Route::get('roles/{role}/edit', [RoleController::class, 'edit'])->name('roles.edit');
        Route::put('roles/{role}', [RoleController::class, 'update'])->name('roles.update');
        Route::post('roles/{role}/assign-user', [RoleController::class, 'assignUser'])->name('roles.assign-user');
        Route::delete('roles/{role}/remove-user', [RoleController::class, 'removeUser'])->name('roles.remove-user');
    });

    Route::middleware('permission:roles.delete')->group(function () {
        Route::delete('roles/{role}', [RoleController::class, 'destroy'])->name('roles.destroy');
    });

    Route::resource('permissions', PermissionController::class)->except(['show']);

    // Secure File Storage System
    Route::middleware('permission:apps.download')->group(function () {
        Route::post('files/{app}/generate-token', [FileStorageController::class, 'generateDownloadToken'])->name('files.generate-token');
    });

    Route::middleware('permission:apps.upload')->group(function () {
        Route::post('files/upload', [FileStorageController::class, 'secureUpload'])->name('files.upload');
        Route::post('files/{app}/release-quarantine', [FileStorageController::class, 'releaseFromQuarantine'])->name('files.release-quarantine');
    });

    Route::middleware('permission:dashboard.read')->group(function () {
        Route::get('files/storage-stats', [FileStorageController::class, 'getStorageStats'])->name('files.storage-stats');
        Route::get('files/storage-management', function () {
            return view('files.storage-management');
        })->name('files.storage-management');
    });

    // Public secure download route (with token validation)
    Route::get('files/{app}/download/{token}', [FileStorageController::class, 'secureDownload'])
         ->name('files.download')
         ->middleware(['secure.file']);

    // Apps Management (Order matters - specific routes before parameterized ones)
    Route::middleware('permission:apps.read')->group(function () {
        Route::get('apps', [AppController::class, 'index'])->name('apps.index');
    });

    Route::middleware('permission:apps.create')->group(function () {
        Route::get('apps/create', [AppController::class, 'create'])->name('apps.create');
        Route::post('apps', [AppController::class, 'store'])->name('apps.store');
        Route::post('apps/upload-chunk', [AppController::class, 'uploadChunk'])->name('apps.upload-chunk');
        Route::post('apps/finalize-upload', [AppController::class, 'finalizeUpload'])->name('apps.finalize-upload');
        Route::post('apps/extract-metadata', [AppController::class, 'extractMetadata'])->name('apps.extract-metadata');
    });

    Route::middleware('permission:apps.read')->group(function () {
        Route::get('apps/{app}', [AppController::class, 'show'])->name('apps.show');
        Route::get('apps/{app}/download-stats', [AppController::class, 'downloadStats'])->name('apps.download-stats');
    });

    Route::middleware('permission:apps.update')->group(function () {
        Route::get('apps/{app}/edit', [AppController::class, 'edit'])->name('apps.edit');
        Route::put('apps/{app}', [AppController::class, 'update'])->name('apps.update');
        Route::post('apps/{app}/release-quarantine', [AppController::class, 'releaseFromQuarantine'])->name('apps.release-quarantine');
        Route::post('apps/{app}/regenerate-token', [AppController::class, 'regenerateToken'])->name('apps.regenerate-token');
    });

    Route::middleware('permission:apps.delete')->group(function () {
        Route::delete('apps/{app}', [AppController::class, 'destroy'])->name('apps.destroy');
    });

    // Sprint Management Routes (order matters - specific routes before parameterized ones)
    Route::middleware('permission:sprints.create')->group(function () {
        Route::get('/sprints/create', [SprintController::class, 'create'])->name('sprints.create');
        Route::post('/sprints', [SprintController::class, 'store'])->name('sprints.store');
    });

    Route::middleware('permission:sprints.read')->group(function () {
        Route::get('/sprints', [SprintController::class, 'index'])->name('sprints.index');
        Route::get('/sprints/{sprint}', [SprintController::class, 'show'])->name('sprints.show');
    });

    Route::middleware('permission:sprints.update')->group(function () {
        Route::get('/sprints/{sprint}/edit', [SprintController::class, 'edit'])->name('sprints.edit');
        Route::put('/sprints/{sprint}', [SprintController::class, 'update'])->name('sprints.update');
        Route::patch('/sprints/{sprint}/status', [SprintController::class, 'updateStatus'])->name('sprints.updateStatus');
        Route::patch('/sprints/{id}/restore', [SprintController::class, 'restore'])->name('sprints.restore');
    });

    Route::middleware('permission:sprints.delete')->group(function () {
        Route::delete('/sprints/{sprint}', [SprintController::class, 'destroy'])->name('sprints.destroy');
    });

    Route::middleware('permission:audit_logs.view')->group(function () {
        Route::get('/audit-logs', [AuditLogController::class, 'index'])->name('audit-logs.index');
        Route::get('/audit-logs/{auditLog}', [AuditLogController::class, 'show'])->name('audit-logs.show');
        Route::get('/audit-logs-export', [AuditLogController::class, 'export'])->name('audit-logs.export');
        Route::get('/audit-logs-stats', [AuditLogController::class, 'stats'])->name('audit-logs.stats');
    });
});

// Test route for debugging metadata extraction
Route::get('/test-metadata', function() {
    return response()->json([
        'zip_available' => class_exists('ZipArchive'),
        'php_version' => PHP_VERSION,
        'temp_dir' => sys_get_temp_dir(),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size'),
        'extensions' => array_filter(get_loaded_extensions(), function($ext) {
            return in_array($ext, ['zip', 'zlib', 'fileinfo']);
        })
    ]);
});

// Test route for creating a sample IPA file
Route::get('/test-create-sample-ipa', function() {
    try {
        $tempDir = sys_get_temp_dir();
        $ipaPath = $tempDir . '/sample-app.ipa';

        // Create a sample IPA structure
        $zip = new ZipArchive();
        if ($zip->open($ipaPath, ZipArchive::CREATE) === TRUE) {
            // Create Payload directory structure
            $zip->addEmptyDir('Payload/');
            $zip->addEmptyDir('Payload/SampleApp.app/');

            // Create a sample Info.plist
            $infoPlist = '<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDisplayName</key>
    <string>Sample App</string>
    <key>CFBundleName</key>
    <string>SampleApp</string>
    <key>CFBundleIdentifier</key>
    <string>com.example.sampleapp</string>
    <key>CFBundleShortVersionString</key>
    <string>1.2.3</string>
    <key>CFBundleVersion</key>
    <string>456</string>
</dict>
</plist>';

            $zip->addFromString('Payload/SampleApp.app/Info.plist', $infoPlist);
            $zip->close();

            return response()->json([
                'success' => true,
                'message' => 'Sample IPA created',
                'path' => $ipaPath,
                'size' => filesize($ipaPath)
            ]);
        } else {
            return response()->json([
                'success' => false,
                'error' => 'Failed to create ZIP file'
            ]);
        }
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
});

// Test route for extracting metadata from sample file
Route::get('/test-extract-sample', function() {
    try {
        $tempDir = sys_get_temp_dir();
        $ipaPath = $tempDir . '/sample-app.ipa';

        if (!file_exists($ipaPath)) {
            return response()->json([
                'success' => false,
                'error' => 'Sample IPA not found. Create it first with /test-create-sample-ipa'
            ]);
        }

        // Create a temporary uploaded file object
        $tempFile = new \Illuminate\Http\UploadedFile(
            $ipaPath,
            'sample-app.ipa',
            'application/octet-stream',
            null,
            true
        );

        // Use the AppController's extraction methods
        $controller = new \App\Http\Controllers\AppController();
        $reflection = new ReflectionClass($controller);

        // Get private methods
        $detectPlatform = $reflection->getMethod('detectPlatform');
        $detectPlatform->setAccessible(true);

        $extractAppMetadata = $reflection->getMethod('extractAppMetadata');
        $extractAppMetadata->setAccessible(true);

        $extractAppInfo = $reflection->getMethod('extractAppInfo');
        $extractAppInfo->setAccessible(true);

        // Test the extraction
        $platform = $detectPlatform->invoke($controller, $tempFile);
        $metadata = $extractAppMetadata->invoke($controller, $tempFile, $platform);
        $appInfo = $extractAppInfo->invoke($controller, $tempFile, $metadata);

        return response()->json([
            'success' => true,
            'platform' => $platform,
            'metadata' => $metadata,
            'app_info' => $appInfo,
            'file_size' => filesize($ipaPath)
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});
