#!/usr/bin/env python3
"""
Binary Plist Parser for iOS App Info.plist files
Extracts app metadata from binary plist format
"""

import sys
import json
import plistlib
import os
import zipfile
import shutil
from pathlib import Path

def parse_plist_file(file_path):
    """
    Parse a plist file (binary or XML) and extract app metadata

    Args:
        file_path (str): Path to the plist file

    Returns:
        dict: Extracted app metadata
    """
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            return {
                'success': False,
                'error': f'File not found: {file_path}'
            }

        # Read the plist file
        with open(file_path, 'rb') as f:
            plist_data = plistlib.load(f)

        # Extract key app information
        app_info = {
            'success': True,
            'platform': 'ios',
            'extraction_method': 'python_plistlib',
            'app_name': get_value_with_fallback(plist_data, [
                'CFBundleDisplayName',
                'CFBundleName'
            ], 'Unknown App'),
            'version': get_value_with_fallback(plist_data, [
                'CFBundleShortVersionString'
            ], '1.0.0'),
            'build_number': get_value_with_fallback(plist_data, [
                'CFBundleVersion'
            ], '1'),
            'bundle_identifier': get_value_with_fallback(plist_data, [
                'CFBundleIdentifier'
            ], 'com.unknown.app'),
            'minimum_os_version': get_value_with_fallback(plist_data, [
                'MinimumOSVersion'
            ], 'Unknown'),
            'executable': get_value_with_fallback(plist_data, [
                'CFBundleExecutable'
            ], 'Unknown'),
            'supported_platforms': plist_data.get('CFBundleSupportedPlatforms', []),
            'device_family': plist_data.get('UIDeviceFamily', []),
            'required_capabilities': plist_data.get('UIRequiredDeviceCapabilities', []),
            'supported_orientations': plist_data.get('UISupportedInterfaceOrientations', []),
            'background_modes': plist_data.get('UIBackgroundModes', []),
            'url_schemes': extract_url_schemes(plist_data),
            'permissions': extract_permissions(plist_data),
            'raw_data': plist_data  # Include full plist data for debugging
        }

        return app_info

    except plistlib.InvalidFileException as e:
        return {
            'success': False,
            'error': f'Invalid plist file format: {str(e)}'
        }
    except Exception as e:
        return {
            'success': False,
            'error': f'Error parsing plist: {str(e)}'
        }

def get_value_with_fallback(data, keys, default=None):
    """
    Get value from dictionary with multiple key fallbacks

    Args:
        data (dict): Dictionary to search
        keys (list): List of keys to try in order
        default: Default value if none found

    Returns:
        Value from first matching key or default
    """
    for key in keys:
        if key in data and data[key]:
            return data[key]
    return default

def extract_url_schemes(plist_data):
    """
    Extract URL schemes from CFBundleURLTypes

    Args:
        plist_data (dict): Plist data

    Returns:
        list: List of URL schemes
    """
    url_types = plist_data.get('CFBundleURLTypes', [])
    schemes = []

    for url_type in url_types:
        if isinstance(url_type, dict):
            type_schemes = url_type.get('CFBundleURLSchemes', [])
            schemes.extend(type_schemes)

    return schemes

def extract_permissions(plist_data):
    """
    Extract permission usage descriptions

    Args:
        plist_data (dict): Plist data

    Returns:
        dict: Dictionary of permissions and their descriptions
    """
    permission_keys = [
        'NSCameraUsageDescription',
        'NSMicrophoneUsageDescription',
        'NSPhotoLibraryUsageDescription',
        'NSPhotoLibraryAddUsageDescription',
        'NSLocationWhenInUseUsageDescription',
        'NSLocationAlwaysUsageDescription',
        'NSContactsUsageDescription',
        'NSCalendarsUsageDescription',
        'NSRemindersUsageDescription',
        'NSMotionUsageDescription',
        'NSHealthUpdateUsageDescription',
        'NSHealthShareUsageDescription',
        'NSBluetoothPeripheralUsageDescription',
        'NSFaceIDUsageDescription',
        'NSUserTrackingUsageDescription'
    ]

    permissions = {}
    for key in permission_keys:
        if key in plist_data:
            permissions[key] = plist_data[key]

    return permissions

def save_extracted_data(app_info, output_file):
    """
    Save extracted app information to JSON file

    Args:
        app_info (dict): Extracted app information
        output_file (str): Output file path
    """
    try:
        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(output_file)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        # Save to JSON file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(app_info, f, indent=2, ensure_ascii=False, default=str)

        print(f"✅ Data saved to: {output_file}")

    except Exception as e:
        print(f"❌ Error saving data: {str(e)}")

def extract_app_icon_from_ipa(ipa_file_path, output_dir, file_uuid):
    """
    Extract app icon from IPA file and save with UUID filename

    Args:
        ipa_file_path (str): Path to the IPA file
        output_dir (str): Directory to save the icon
        file_uuid (str): UUID to use as filename

    Returns:
        str: Path to extracted icon file or None if not found
    """
    try:
        if not os.path.exists(ipa_file_path):
            print(f"❌ IPA file not found: {ipa_file_path}")
            return None

        with zipfile.ZipFile(ipa_file_path, 'r') as zip_file:
            # Look for app icon files in the IPA
            icon_candidates = []

            for file_info in zip_file.filelist:
                filename = file_info.filename

                # Look for icon files in the app bundle
                if (filename.startswith('Payload/') and
                    '.app/' in filename and
                    not filename.endswith('.app/') and
                    filename.lower().endswith(('.png', '.jpg', '.jpeg'))):

                    # Check if it's likely an icon file
                    basename = os.path.basename(filename).lower()
                    if (any(icon_name in basename for icon_name in ['icon', 'appicon']) or
                        basename.startswith('icon') or
                        'appicon' in basename):

                        # Get file size to prioritize larger icons
                        file_size = file_info.file_size
                        icon_candidates.append((filename, file_size))

            if not icon_candidates:
                print("⚠️ No app icon found in IPA")
                return None

            # Sort by file size (larger icons first)
            icon_candidates.sort(key=lambda x: x[1], reverse=True)
            best_icon = icon_candidates[0][0]

            print(f"📱 Found app icon: {best_icon} ({icon_candidates[0][1]} bytes)")

            # Extract the icon
            icon_data = zip_file.read(best_icon)

            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)

            # Get file extension
            _, ext = os.path.splitext(best_icon)
            if not ext:
                ext = '.png'  # Default to PNG

            # Save with UUID filename
            icon_filename = f"{file_uuid}{ext}"
            icon_path = os.path.join(output_dir, icon_filename)

            with open(icon_path, 'wb') as icon_file:
                icon_file.write(icon_data)

            print(f"✅ App icon saved: {icon_path}")
            return icon_path

    except Exception as e:
        print(f"❌ Error extracting app icon: {str(e)}")
        return None

def main():
    """
    Main function - parse command line arguments and process plist file
    """
    if len(sys.argv) < 2:
        print("Usage: python parse_plist.py <plist_file_path> [output_file] [ipa_file_path] [file_uuid]")
        print("Example: python parse_plist.py /path/to/Info.plist /path/to/output.json /path/to/app.ipa uuid-123")
        sys.exit(1)

    plist_file = sys.argv[1]

    # Generate output file name based on input file if not provided
    if len(sys.argv) >= 3:
        output_file = sys.argv[2]
    else:
        # Create output file in same directory with .json extension
        base_name = os.path.splitext(os.path.basename(plist_file))[0]
        output_dir = os.path.dirname(plist_file)
        output_file = os.path.join(output_dir, f"{base_name}_extracted.json")

    # Get IPA file path and UUID if provided
    ipa_file_path = sys.argv[3] if len(sys.argv) >= 4 else None
    file_uuid = sys.argv[4] if len(sys.argv) >= 5 else None

    print(f"🔍 Parsing plist file: {plist_file}")
    print(f"💾 Output file: {output_file}")
    if ipa_file_path:
        print(f"📦 IPA file: {ipa_file_path}")
    if file_uuid:
        print(f"🆔 File UUID: {file_uuid}")

    # Parse the plist file
    app_info = parse_plist_file(plist_file)

    # Extract app icon if IPA file and UUID are provided
    if ipa_file_path and file_uuid and app_info.get('success'):
        # Create temp icon directory
        temp_icon_dir = os.path.join(os.path.dirname(output_file), '..', 'temp_icons')
        icon_path = extract_app_icon_from_ipa(ipa_file_path, temp_icon_dir, file_uuid)

        if icon_path:
            app_info['icon_path'] = icon_path
            app_info['icon_filename'] = os.path.basename(icon_path)

    # Print results
    if app_info['success']:
        print("✅ Successfully parsed plist file!")
        print(f"📱 App Name: {app_info['app_name']}")
        print(f"🏷️  Bundle ID: {app_info['bundle_identifier']}")
        print(f"📊 Version: {app_info['version']} (Build: {app_info['build_number']})")
        print(f"📱 Min OS: {app_info['minimum_os_version']}")
        print(f"🔗 URL Schemes: {', '.join(app_info['url_schemes']) if app_info['url_schemes'] else 'None'}")
        print(f"🔐 Permissions: {len(app_info['permissions'])} found")
    else:
        print(f"❌ Error: {app_info['error']}")

    # Save extracted data
    save_extracted_data(app_info, output_file)

    # Output JSON to stdout for PHP to capture
    print("=== JSON_OUTPUT_START ===")
    print(json.dumps(app_info, default=str))
    print("=== JSON_OUTPUT_END ===")

if __name__ == "__main__":
    main()
