<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions for each module with CRUD operations
        $modules = [
            'users' => 'User Management',
            'roles' => 'Role & Permission',
            'dashboard' => 'Dashboard',
            'apps' => 'Apps Management',
            'sprints' => 'Sprint Management',
        ];

        $actions = ['create', 'read', 'update', 'delete'];

        foreach ($modules as $module => $description) {
            foreach ($actions as $action) {
                Permission::firstOrCreate([
                    'name' => "{$module}.{$action}",
                    'guard_name' => 'web',
                ]);
            }
        }

        // Create special permissions
        $specialPermissions = [
            'apps.download' => 'Download apps',
            'apps.upload' => 'Upload apps',
            'apps.view_details' => 'View app details',
            'sprints.end' => 'End sprints',
            'sprints.cleanup' => 'Cleanup sprint storage',
            'audit_logs.view' => 'View audit logs',
            'system.admin' => 'System administration',
        ];

        foreach ($specialPermissions as $permission => $description) {
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web',
            ]);
        }

        // Create roles
        $superAdmin = Role::firstOrCreate([
            'name' => 'Super Admin',
            'guard_name' => 'web',
        ]);

        $admin = Role::firstOrCreate([
            'name' => 'Admin',
            'guard_name' => 'web',
        ]);

        $developer = Role::firstOrCreate([
            'name' => 'Developer',
            'guard_name' => 'web',
        ]);

        $viewer = Role::firstOrCreate([
            'name' => 'Viewer',
            'guard_name' => 'web',
        ]);

        // Assign all permissions to Super Admin
        $superAdmin->givePermissionTo(Permission::all());

        // Assign permissions to Admin (all except system admin)
        $adminPermissions = Permission::where('name', '!=', 'system.admin')->get();
        $admin->givePermissionTo($adminPermissions);

        // Assign permissions to Developer
        $developerPermissions = [
            'dashboard.read',
            'apps.create', 'apps.read', 'apps.update', 'apps.download', 'apps.upload', 'apps.view_details',
            'sprints.read',
        ];
        $developer->givePermissionTo($developerPermissions);

        // Assign permissions to Viewer
        $viewerPermissions = [
            'dashboard.read',
            'apps.read', 'apps.download', 'apps.view_details',
            'sprints.read',
        ];
        $viewer->givePermissionTo($viewerPermissions);

        // Create default Super Admin user
        $superAdminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Administrator',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
                'is_active' => true,
                'password_changed_at' => now(),
            ]
        );

        $superAdminUser->assignRole('Super Admin');

        // Create sample admin user
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
                'is_active' => true,
                'password_changed_at' => now(),
            ]
        );

        $adminUser->assignRole('Admin');

        // Create sample developer user
        $developerUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Developer User',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
                'is_active' => true,
                'password_changed_at' => now(),
            ]
        );

        $developerUser->assignRole('Developer');

        $this->command->info('Roles, permissions, and default users created successfully!');
        $this->command->info('Default Super Admin: <EMAIL> / password123');
        $this->command->info('Default Admin: <EMAIL> / password123');
        $this->command->info('Default Developer: <EMAIL> / password123');
    }
}
