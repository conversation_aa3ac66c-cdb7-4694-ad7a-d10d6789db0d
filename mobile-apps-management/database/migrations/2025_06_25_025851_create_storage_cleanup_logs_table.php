<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('storage_cleanup_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sprint_id')->nullable()->constrained('sprints')->onDelete('set null');
            $table->string('sprint_name'); // Store name for deleted sprints
            $table->foreignId('cleaned_by')->constrained('users')->onDelete('cascade');
            $table->unsignedInteger('files_deleted_count')->default(0);
            $table->unsignedBigInteger('storage_freed_mb')->default(0);
            $table->json('cleanup_summary'); // Details of what was cleaned
            $table->json('apps_deleted')->nullable(); // Array of deleted app IDs and names
            $table->enum('cleanup_type', ['manual', 'scheduled', 'automatic'])->default('manual');
            $table->text('notes')->nullable();
            $table->boolean('success')->default(true);
            $table->text('error_message')->nullable();
            $table->timestamp('cleanup_date');
            $table->timestamps();

            $table->index(['sprint_id', 'cleanup_date']);
            $table->index(['cleaned_by', 'cleanup_date']);
            $table->index('cleanup_type');
            $table->index('success');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('storage_cleanup_logs');
    }
};
