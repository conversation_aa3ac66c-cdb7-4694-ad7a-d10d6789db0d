<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For SQLite, we need to handle this differently due to index constraints
        Schema::table('sprints', function (Blueprint $table) {
            // First, drop the index that references the status column
            $table->dropIndex('sprints_status_end_date_index');
        });

        // Update existing data to match new enum values
        DB::statement("UPDATE sprints SET status = 'completed' WHERE status = 'ended'");

        // For SQLite, we need to use raw SQL to change the column type
        DB::statement("
            CREATE TABLE sprints_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                start_date DATE,
                end_date DATE,
                status VARCHAR(255) CHECK (status IN ('planning', 'active', 'completed', 'cancelled')) DEFAULT 'planning',
                storage_size_mb INTEGER DEFAULT 0,
                files_count INTEGER DEFAULT 0,
                cleanup_eligible BOOLEAN DEFAULT 0,
                last_cleanup_check TIMESTAMP,
                created_by INTEGER NOT NULL,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,
                deleted_at TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
            )
        ");

        // Copy data from old table to new table
        DB::statement("
            INSERT INTO sprints_new (id, name, description, start_date, end_date, status, storage_size_mb, files_count, cleanup_eligible, last_cleanup_check, created_by, created_at, updated_at, deleted_at)
            SELECT id, name, description, start_date, end_date, status, storage_size_mb, files_count, cleanup_eligible, last_cleanup_check, created_by, created_at, updated_at, deleted_at
            FROM sprints
        ");

        // Drop old table and rename new table
        DB::statement("DROP TABLE sprints");
        DB::statement("ALTER TABLE sprints_new RENAME TO sprints");

        // Recreate indexes
        Schema::table('sprints', function (Blueprint $table) {
            $table->index(['status', 'end_date']);
            $table->index('cleanup_eligible');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sprints', function (Blueprint $table) {
            // Revert back to original enum values
            DB::statement("UPDATE sprints SET status = 'ended' WHERE status = 'completed'");

            $table->dropColumn('status');
        });

        Schema::table('sprints', function (Blueprint $table) {
            $table->enum('status', ['active', 'ended'])->default('active')->after('end_date');
        });
    }
};
