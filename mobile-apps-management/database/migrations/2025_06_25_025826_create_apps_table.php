<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('apps', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('bundle_identifier');
            $table->string('version');
            $table->string('build_number');
            $table->enum('platform', ['ios', 'android', 'huawei']);
            $table->string('file_path');
            $table->string('file_hash', 64); // SHA-256 hash
            $table->unsignedBigInteger('file_size'); // in bytes
            $table->string('icon_path')->nullable();
            $table->text('changelog')->nullable();
            $table->string('ticket_link')->nullable();
            $table->string('qr_code_path')->nullable();
            $table->string('access_token', 64)->unique(); // for secure file access
            $table->json('metadata')->nullable(); // additional app metadata
            $table->unsignedInteger('download_count')->default(0);
            $table->foreignId('sprint_id')->constrained('sprints')->onDelete('cascade');
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');
            $table->timestamp('upload_date');
            $table->timestamps();

            $table->index(['platform', 'sprint_id']);
            $table->index(['bundle_identifier', 'version']);
            $table->index('access_token');
            $table->index('upload_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('apps');
    }
};
