<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('file_access_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('app_id')->constrained('apps')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('user_email')->nullable(); // Store email for deleted users
            $table->enum('access_type', ['download', 'view', 'qr_scan', 'api_access']);
            $table->ipAddress('ip_address');
            $table->text('user_agent')->nullable();
            $table->string('file_path');
            $table->string('access_token')->nullable(); // Token used for access
            $table->boolean('success')->default(true);
            $table->text('error_message')->nullable();
            $table->string('download_method')->nullable(); // direct, qr_code, api
            $table->string('device_type')->nullable(); // detected from user agent
            $table->string('platform_detected')->nullable(); // ios, android, web
            $table->json('metadata')->nullable(); // Additional context
            $table->timestamp('access_date');
            $table->timestamps();

            $table->index(['app_id', 'access_date']);
            $table->index(['user_id', 'access_date']);
            $table->index(['ip_address', 'access_date']);
            $table->index(['access_type', 'success']);
            $table->index('access_token');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('file_access_logs');
    }
};
