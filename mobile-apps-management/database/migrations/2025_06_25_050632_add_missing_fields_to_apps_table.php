<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('apps', function (Blueprint $table) {
            $table->string('file_name')->nullable()->after('file_path');
            $table->string('mime_type')->nullable()->after('file_size');
            $table->enum('status', ['quarantined', 'active', 'inactive'])->default('quarantined')->after('metadata');
            $table->timestamp('quarantine_until')->nullable()->after('status');
            $table->foreignId('released_by')->nullable()->constrained('users')->after('quarantine_until');
            $table->timestamp('released_at')->nullable()->after('released_by');

            $table->index('status');
            $table->index('quarantine_until');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('apps', function (Blueprint $table) {
            $table->dropIndex(['status']);
            $table->dropIndex(['quarantine_until']);
            $table->dropForeign(['released_by']);
            $table->dropColumn([
                'file_name',
                'mime_type',
                'status',
                'quarantine_until',
                'released_by',
                'released_at'
            ]);
        });
    }
};
